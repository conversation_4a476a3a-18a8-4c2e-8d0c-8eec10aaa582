<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard Chart Fixes Test</title>
    <link rel="stylesheet" href="snapapp.css">
    <link rel="stylesheet" href="components/charts/snap-charts.css">
    <style>
        body {
            font-family: 'Amazon Ember', Arial, sans-serif;
            background: var(--bg-secondary);
            color: var(--text-primary);
            margin: 0;
            padding: 20px;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            margin-bottom: 40px;
            background: var(--bg-primary);
            border: 1.5px solid var(--border-color);
            border-radius: 14px;
            padding: 24px;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 16px;
            color: var(--text-primary);
        }
        
        .test-description {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 20px;
        }
        
        /* Simulate dashboard card styling */
        .dashboard-card-simulation {
            width: 100%;
            background: var(--bg-primary);
            border: 1.5px solid var(--border-color);
            border-radius: 14px;
            padding: 24px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            margin-top: 16px;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0px;
            width: 100%;
        }
        
        .title-section {
            display: flex;
            align-items: center;
            gap: 10px;
            flex: 1;
            min-width: 0;
        }
        
        .title-text {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .card-title {
            font-size: 16px;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        .card-date {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-secondary);
        }
        
        /* Test different container widths */
        .width-test-800 { width: 800px; }
        .width-test-600 { width: 600px; }
        .width-test-400 { width: 400px; }
        
        .status {
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 16px;
        }
        
        .status.success {
            background: #d1fae5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .status.error {
            background: #fee2e2;
            color: #991b1b;
            border: 1px solid #fca5a5;
        }
        
        .status.warning {
            background: #fef3c7;
            color: #92400e;
            border: 1px solid #fde68a;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Dashboard Chart Fixes Test</h1>
        <p>Testing tooltip functionality and right-anchored scrolling behavior in production mode (dashboard context).</p>
        
        <!-- Test 1: Tooltip Functionality -->
        <div class="test-section">
            <div class="test-title">Test 1: Tooltip Functionality in Production Mode</div>
            <div class="test-description">
                This chart simulates the dashboard implementation with showContainer: false. 
                Hover over columns to test tooltip positioning and visibility.
            </div>
            <div id="tooltip-test-status" class="status warning">Initializing chart...</div>
            
            <div class="dashboard-card-simulation">
                <div class="card-header">
                    <div class="title-section">
                        <div class="title-text">
                            <span class="card-title">Today vs Previous Years</span>
                            <span class="card-date">Dec 28</span>
                        </div>
                    </div>
                </div>
                <div id="tooltip-test-chart" class="today-vs-previous-years-chart-container"></div>
            </div>
        </div>
        
        <!-- Test 2: Right-Anchored Scrolling - Normal Width -->
        <div class="test-section">
            <div class="test-title">Test 2: Right-Anchored Scrolling - Normal Width (800px)</div>
            <div class="test-description">
                Chart should show rightmost columns (current year) with 32px right padding. 
                Historical years should be scrollable to the left.
            </div>
            <div id="scroll-test-800-status" class="status warning">Initializing chart...</div>
            
            <div class="dashboard-card-simulation width-test-800">
                <div class="card-header">
                    <div class="title-section">
                        <div class="title-text">
                            <span class="card-title">Today vs Previous Years</span>
                            <span class="card-date">Dec 28</span>
                        </div>
                    </div>
                </div>
                <div id="scroll-test-chart-800" class="today-vs-previous-years-chart-container"></div>
            </div>
        </div>
        
        <!-- Test 3: Right-Anchored Scrolling - Constrained Width -->
        <div class="test-section">
            <div class="test-title">Test 3: Right-Anchored Scrolling - Constrained Width (600px)</div>
            <div class="test-description">
                Chart should maintain right-anchored behavior even with limited width. 
                Current year should remain visible on the right.
            </div>
            <div id="scroll-test-600-status" class="status warning">Initializing chart...</div>
            
            <div class="dashboard-card-simulation width-test-600">
                <div class="card-header">
                    <div class="title-section">
                        <div class="title-text">
                            <span class="card-title">Today vs Previous Years</span>
                            <span class="card-date">Dec 28</span>
                        </div>
                    </div>
                </div>
                <div id="scroll-test-chart-600" class="today-vs-previous-years-chart-container"></div>
            </div>
        </div>
        
        <!-- Test 4: Right-Anchored Scrolling - Very Constrained Width -->
        <div class="test-section">
            <div class="test-title">Test 4: Right-Anchored Scrolling - Very Constrained Width (400px)</div>
            <div class="test-description">
                Chart should still maintain right-anchored behavior with very limited width. 
                Only a few recent years should be visible.
            </div>
            <div id="scroll-test-400-status" class="status warning">Initializing chart...</div>
            
            <div class="dashboard-card-simulation width-test-400">
                <div class="card-header">
                    <div class="title-section">
                        <div class="title-text">
                            <span class="card-title">Today vs Previous Years</span>
                            <span class="card-date">Dec 28</span>
                        </div>
                    </div>
                </div>
                <div id="scroll-test-chart-400" class="today-vs-previous-years-chart-container"></div>
            </div>
        </div>
    </div>

    <script src="components/charts/snap-charts.js"></script>
    <script src="components/dashboard/dashboard.js"></script>
    <script>
        // Generate test data (same as dashboard)
        function generateTestData() {
            const data = [];
            const startYear = 2000;
            const endYear = 2025;
            
            // Get today's month and day for consistent date across all years
            const today = new Date();
            const month = today.toLocaleDateString('en-US', { month: 'short' });
            const day = today.getDate();
            
            for (let year = startYear; year <= endYear; year++) {
                const yearData = {
                    month: `${month} ${day}, ${year}`,
                    day: day,
                    year: year.toString().slice(-2),
                    sales: Math.floor(Math.random() * 50) + 10, // 10-60 sales
                    returns: Math.floor(Math.random() * 5), // 0-5 returns
                    royalties: Math.floor(Math.random() * 20) + 5, // 5-25 royalties
                    marketplaces: {
                        'amazon-us': Math.floor(Math.random() * 20) + 5,
                        'amazon-uk': Math.floor(Math.random() * 15) + 3,
                        'amazon-de': Math.floor(Math.random() * 10) + 2,
                        'etsy': Math.floor(Math.random() * 8) + 1,
                        'redbubble': Math.floor(Math.random() * 12) + 2
                    }
                };
                data.push(yearData);
            }
            
            return data;
        }
        
        // Initialize test charts
        function initializeTestCharts() {
            const testData = generateTestData();
            
            // Test 1: Tooltip functionality
            try {
                const tooltipChart = new SnapChart({
                    container: '#tooltip-test-chart',
                    type: 'scrollable-stacked-column',
                    data: testData,
                    demoOptions: {
                        showContainer: false,
                        showTitle: false,
                        showDataEditor: false,
                        showControls: false,
                        showInsights: false
                    },
                    options: {
                        responsive: true,
                        animate: true,
                        height: 300,
                        isTodayVsPreviousYearsChart: true,
                        fullWidthDistribution: true
                    }
                });
                
                tooltipChart.init();
                document.getElementById('tooltip-test-status').textContent = 'Chart initialized. Hover over columns to test tooltips.';
                document.getElementById('tooltip-test-status').className = 'status success';
            } catch (error) {
                document.getElementById('tooltip-test-status').textContent = `Error: ${error.message}`;
                document.getElementById('tooltip-test-status').className = 'status error';
            }
            
            // Test 2: 800px width
            try {
                const chart800 = new SnapChart({
                    container: '#scroll-test-chart-800',
                    type: 'scrollable-stacked-column',
                    data: testData,
                    demoOptions: {
                        showContainer: false,
                        showTitle: false,
                        showDataEditor: false,
                        showControls: false,
                        showInsights: false
                    },
                    options: {
                        responsive: true,
                        animate: true,
                        height: 300,
                        isTodayVsPreviousYearsChart: true,
                        fullWidthDistribution: true
                    }
                });
                
                chart800.init();
                document.getElementById('scroll-test-800-status').textContent = 'Chart initialized. Check right-anchored positioning.';
                document.getElementById('scroll-test-800-status').className = 'status success';
            } catch (error) {
                document.getElementById('scroll-test-800-status').textContent = `Error: ${error.message}`;
                document.getElementById('scroll-test-800-status').className = 'status error';
            }
            
            // Test 3: 600px width
            try {
                const chart600 = new SnapChart({
                    container: '#scroll-test-chart-600',
                    type: 'scrollable-stacked-column',
                    data: testData,
                    demoOptions: {
                        showContainer: false,
                        showTitle: false,
                        showDataEditor: false,
                        showControls: false,
                        showInsights: false
                    },
                    options: {
                        responsive: true,
                        animate: true,
                        height: 300,
                        isTodayVsPreviousYearsChart: true,
                        fullWidthDistribution: true
                    }
                });
                
                chart600.init();
                document.getElementById('scroll-test-600-status').textContent = 'Chart initialized. Check right-anchored positioning.';
                document.getElementById('scroll-test-600-status').className = 'status success';
            } catch (error) {
                document.getElementById('scroll-test-600-status').textContent = `Error: ${error.message}`;
                document.getElementById('scroll-test-600-status').className = 'status error';
            }
            
            // Test 4: 400px width
            try {
                const chart400 = new SnapChart({
                    container: '#scroll-test-chart-400',
                    type: 'scrollable-stacked-column',
                    data: testData,
                    demoOptions: {
                        showContainer: false,
                        showTitle: false,
                        showDataEditor: false,
                        showControls: false,
                        showInsights: false
                    },
                    options: {
                        responsive: true,
                        animate: true,
                        height: 300,
                        isTodayVsPreviousYearsChart: true,
                        fullWidthDistribution: true
                    }
                });
                
                chart400.init();
                document.getElementById('scroll-test-400-status').textContent = 'Chart initialized. Check right-anchored positioning.';
                document.getElementById('scroll-test-400-status').className = 'status success';
            } catch (error) {
                document.getElementById('scroll-test-400-status').textContent = `Error: ${error.message}`;
                document.getElementById('scroll-test-400-status').className = 'status error';
            }
        }
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', initializeTestCharts);
    </script>
</body>
</html>

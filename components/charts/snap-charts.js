/**
 * ============================================================================
 * SNAP CHARTS - JavaScript Chart Engine
 * ============================================================================
 * 
 * A lightweight, theme-aware charting library built specifically for
 * the Snap for MOD application.
 * 
 * Features:
 * - SVG-based rendering for crisp graphics
 * - Automatic light/dark theme integration
 * - Responsive design
 * - Smooth animations
 * - Accessible by default
 * 
 * @version 1.0.0
 * <AUTHOR> for MOD Team
 */

class SnapChart {
  constructor(options = {}) {
    this.container = options.container;
    this.type = options.type || 'stacked-column';
    this.data = options.data || [];
    
    // Demo component configuration
    this.demoOptions = {
      showContainer: options.showContainer !== false,    // Default: true
      showTitle: options.showTitle !== false,           // Default: true  
      showDataEditor: options.showDataEditor !== false, // Default: true
      showControls: options.showControls !== false,     // Default: true
      showInsights: options.showInsights !== false,     // Default: true
      ...options.demoOptions
    };
    
    this.options = {
      responsive: true,
      animate: true,
      theme: 'auto',
      width: null,
      height: options.type === 'daily-sales-history' ? 400 : 251, // Further increased height for daily sales history to accommodate date range controls and year labels with proper padding
      colors: [
        '#8562FF', '#439DB8', '#18B2B7', '#26D1A5', 
        '#4FED7D', '#9FED5E', '#F2FF65'
      ],
      // Date range will be set from provided data or options
      currentStartDate: null,
      currentEndDate: null,
      // Initialize allTimeData with the same data as main data for consistency
      allTimeData: options.data || [],
      ...options.options
    };
    
    this.svg = null;
    this.tooltip = null;
    this.isInitialized = false;
    this.themeObserver = null; // Store observer reference for cleanup
    this.isLabelDrillDown = false; // Flag to track when showing label drill-down view (month/quarter/year)
    
    // Performance optimization: throttle expensive chart updates during slider drag
    this.rafId = null;
    this.pendingStartDate = null;
    this.pendingEndDate = null;
    
    // Tab state management
    this.isTabInitiated = false;
    
    // Period tracking system for maintaining consistent navigation
    this.currentPeriodType = null; // 'tab', 'label', 'manual'
    this.currentPeriodDuration = null; // Duration in days for consistent navigation
    this.currentPeriodId = null; // Tab ID or label type for reference // Flag to track tab-initiated changes
    
    // Marketplace flag mapping system for tooltip integration
    this.marketplaceMapping = {
      'US': { flag: 'US.svg', label: 'US', currency: '$', colorIndex: 1, color: '#6033FF' },
      'UK': { flag: 'UK.svg', label: 'UK', currency: '£', colorIndex: 2, color: '#D450FF' },
      'DE': { flag: 'DE.svg', label: 'DE', currency: '€', colorIndex: 3, color: '#FE40C7' },
      'FR': { flag: 'FR.svg', label: 'FR', currency: '€', colorIndex: 4, color: '#FF335E' },
      'IT': { flag: 'IT.svg', label: 'IT', currency: '€', colorIndex: 5, color: '#FF5900' },
      'ES': { flag: 'ES.svg', label: 'ES', currency: '€', colorIndex: 6, color: '#36C7D3' },
      'JP': { flag: 'JP.svg', label: 'JP', currency: '¥', colorIndex: 7, color: '#3BD52F' }
    };
    
    // Flag SVG cache for performance
    this.flagSvgCache = {};
    
    // Royalties points storage for dot positioning
    this.royaltiesPoints = [];
    
    // Bind methods
    this.handleResize = this.handleResize.bind(this);
    this.handleThemeChange = this.handleThemeChange.bind(this);
    
    this.init();
  }
  
  /**
   * Initialize the chart
   */
  init() {
    if (!this.container) {
      console.error('SnapChart: Container element is required');
      return;
    }
    
    // Get container element
    if (typeof this.container === 'string') {
      this.containerElement = document.querySelector(this.container);
    } else {
      this.containerElement = this.container;
    }
    
    if (!this.containerElement) {
      console.error('SnapChart: Container element not found');
      return;
    }
    
    // Process data to ensure totals are calculated correctly
    if (this.data && this.data.length > 0) {
      this.data = this.processEditorData(this.data);
      // Store original data for reset functionality
      this.originalData = JSON.parse(JSON.stringify(this.data));
    }
    
    this.setupChart();
    this.render();
    this.setupEventListeners();
    
    this.isInitialized = true;
  }
  
  /**
   * Set up the basic chart structure
   */
  setupChart() {
    // Clear existing content
    this.containerElement.innerHTML = '';
    
    // Create title section (if enabled)
    if (this.demoOptions.showTitle && (this.options.title || this.options.subtitle)) {
      const titleSection = this.createTitleSection();
      if (titleSection) {
        this.containerElement.appendChild(titleSection);
      }
    }
    
    // Create chart container (if enabled) or use direct canvas
    if (this.demoOptions.showContainer) {
      // Demo mode: Create container with border
      const chartContainer = document.createElement('div');
      chartContainer.classList.add('snap-chart');
      
      const header = this.createHeader();
      this.canvas = this.createCanvas();
      
      chartContainer.appendChild(header);
      chartContainer.appendChild(this.canvas);
      this.containerElement.appendChild(chartContainer);
      
      // Store reference to chart container for styling and positioning
      this.chartContainer = chartContainer;
    } else {
      // Production mode: Create canvas directly
      this.canvas = this.createCanvas();
      this.containerElement.appendChild(this.canvas);
    }
    
    // Create tooltip (always needed)
    this.createTooltip();
    
    // Create data editor (if enabled)
    if (this.demoOptions.showDataEditor) {
      this.createDataEditor();
    }
  }

  /**
   * Create title section with title and subtitle (displayed above chart container)
   */
  createTitleSection() {
    // Only create title section if we have title or subtitle
    if (!this.options.title && !this.options.subtitle) {
      return null;
    }
    
    const titleSection = document.createElement('div');
    titleSection.className = 'snap-chart-title-section';
    
    if (this.options.icon) {
      const icon = document.createElement('div');
      icon.className = 'snap-chart-icon';
      icon.innerHTML = this.options.icon;
      titleSection.appendChild(icon);
    }
    
    const titleContainer = document.createElement('div');
    
    if (this.options.title) {
      const title = document.createElement('h3');
      title.className = 'snap-chart-title';
      title.textContent = this.options.title;
      titleContainer.appendChild(title);
    }
    
    if (this.options.subtitle) {
      const subtitle = document.createElement('p');
      subtitle.className = 'snap-chart-subtitle';
      subtitle.textContent = this.options.subtitle;
      titleContainer.appendChild(subtitle);
    }
    
    titleSection.appendChild(titleContainer);
    
    return titleSection;
  }

  /**
   * Create chart header with controls only (title/subtitle moved to separate section)
   */
  createHeader() {
    const header = document.createElement('div');
    header.className = 'snap-chart-header';
    
    // Controls section (only if enabled)
    if (this.demoOptions.showControls) {
      const controls = document.createElement('div');
      controls.className = 'snap-chart-controls';
      
      if (this.options.dropdown) {
        const dropdown = document.createElement('div');
        dropdown.className = 'snap-chart-dropdown';
        dropdown.innerHTML = this.options.dropdown;
        controls.appendChild(dropdown);
      }
      
      if (this.options.button) {
        const button = document.createElement('button');
        button.className = 'snap-chart-button';
        button.innerHTML = `
          ${this.options.button.text}
          ${this.options.button.icon ? `<span class="snap-chart-button-icon">${this.options.button.icon}</span>` : ''}
        `;
        if (this.options.button.onClick) {
          button.addEventListener('click', this.options.button.onClick);
        }
        controls.appendChild(button);
      }
      
      // Only add controls if we have any
      if (controls.children.length > 0) {
        header.appendChild(controls);
      }
    }
    
    // Add filtering tabs for daily-sales-history charts (always include - these are core functionality)
    if (this.type === 'daily-sales-history') {
      const filterTabs = this.createFilterTabs();
      header.appendChild(filterTabs);
      
      // Add insights component below filter tabs (only if enabled)
      if (this.demoOptions.showInsights) {
        const insights = this.createInsights();
        header.appendChild(insights);
      }
    }
    
    return header;
  }
  
  /**
   * Create filtering tabs for daily-sales-history charts
   */
  createFilterTabs() {
    const filterContainer = document.createElement('div');
    filterContainer.className = 'snap-chart-filter-tabs';
    
    const filterTabs = [
      { id: 'last-7-days', label: 'Last 7 Days' },
      { id: 'last-14-days', label: 'Last 14 Days' },
      { id: 'last-30-days', label: 'Last 30 Days' },
      { id: 'current-month', label: 'Current Month' },
      { id: 'previous-month', label: 'Previous Month' },
      { id: 'last-6-months', label: 'Last 6 Months' },
      { id: 'current-year', label: 'Current Year' },
      { id: 'all-times', label: 'Lifetime' }
    ];
    
    filterTabs.forEach(tab => {
      const tabElement = document.createElement('div');
      tabElement.className = `snap-chart-filter-tab ${tab.id}-tab`;
      tabElement.setAttribute('data-filter', tab.id);
      
      const tabMain = document.createElement('div');
      tabMain.className = 'tab-main';
      
      const tabLabel = document.createElement('span');
      tabLabel.className = 'tab-label';
      tabLabel.textContent = tab.label;
      
      tabMain.appendChild(tabLabel);
      tabElement.appendChild(tabMain);
      
      // Add click event listener
      tabElement.addEventListener('click', () => {
        this.handleFilterTabClick(tab.id);
      });
      
      filterContainer.appendChild(tabElement);
    });
    
    // Update tab states after creation and activate Current Month by default
    setTimeout(() => {
      this.updateTabStates();
      this.activateDefaultTab();
    }, 0);
    
    return filterContainer;
  }

  /**
   * Create insights component for daily-sales-history charts
   */
  createInsights() {
    const insightsContainer = document.createElement('div');
    insightsContainer.className = 'snap-chart-insights';
    
    // Create the seven insights based on Figma design
    const insightsData = [
      { id: 'units-sold', label: '(Units) Sold', value: '0' },
      { id: 'royalties', label: 'Royalties', value: '$0' },
      { id: 'units-returned', label: '(Units) Returned', value: '0', isUnitsReturned: true },
      { id: 'return-rate', label: 'Return Rate', value: '0%', isReturnRate: true },
      { id: 'days-sold', label: '(Days) With Sales', value: '0' },
      { id: 'days-no-sales', label: '(Days) No Sales', value: '0' },
      { id: 'conversion-days-percentage', label: 'Conversion Days %', value: '0%', isConversionDaysPercentage: true }
    ];
    
    insightsData.forEach(insight => {
      const insightElement = document.createElement('div');
      insightElement.className = 'snap-chart-insight';
      insightElement.setAttribute('data-insight', insight.id);
      
      // Create label
      const label = document.createElement('div');
      label.className = 'snap-chart-insight-label';
      label.textContent = insight.label;
      
      // Create value
      const value = document.createElement('div');
      const specialClass = insight.isReturnRate ? 'return-rate' : 
                          (insight.isUnitsReturned ? 'units-returned' : 
                          (insight.isConversionDaysPercentage ? 'conversion-days-percentage' : ''));
      const isZeroValue = (insight.isReturnRate && (insight.value === '0%' || insight.value === '0.0%')) || 
                         (insight.isUnitsReturned && insight.value === '0') ||
                         (insight.isConversionDaysPercentage && (insight.value === '0%' || insight.value === '0.0%'));
      const zeroClass = isZeroValue ? 'zero-value' : '';
      value.className = `snap-chart-insight-value ${specialClass} ${zeroClass}`;
      value.textContent = insight.value;
      
      insightElement.appendChild(label);
      insightElement.appendChild(value);
      insightsContainer.appendChild(insightElement);
    });
    
    return insightsContainer;
  }

  /**
   * Calculate and update insights based on current chart data
   */
  updateInsights() {
    if (this.type !== 'daily-sales-history' || !this.data) return;
    
    const insights = this.calculateInsights(this.data);
    
    // Update each insight value in the DOM
    Object.keys(insights).forEach(key => {
      const insightElement = this.containerElement.querySelector(`[data-insight="${key}"] .snap-chart-insight-value`);
      if (insightElement) {
        insightElement.textContent = insights[key];
        
        // Add zero-value class for return-rate, units-returned, and conversion-days-percentage when they are zero
        if ((key === 'return-rate' || key === 'units-returned' || key === 'conversion-days-percentage')) {
          const isZero = key === 'return-rate' || key === 'conversion-days-percentage' ? 
            insights[key] === '0%' || insights[key] === '0.0%' : 
            insights[key] === '0';
            
          if (isZero) {
            insightElement.classList.add('zero-value');
          } else {
            insightElement.classList.remove('zero-value');
          }
        }
      }
    });
  }

  /**
   * Calculate insights from current data
   */
  calculateInsights(data) {
    if (!data || data.length === 0) {
      return {
        'units-sold': '0',
        'royalties': '$0',
        'units-returned': '0',
        'return-rate': '0%',
        'days-sold': '0',
        'days-no-sales': '0',
        'conversion-days-percentage': '0%'
      };
    }
    
    // Calculate totals
    let totalSales = 0;
    let totalRoyalties = 0;
    let totalReturns = 0;
    let daysSold = 0;
    let daysNoSales = 0;
    
    data.forEach(day => {
      const sales = day.sales || 0;
      const royalties = day.royalties || 0;
      const returns = day.returns || 0;
      
      totalSales += sales;
      totalRoyalties += royalties;
      totalReturns += returns;
      
      if (sales > 0) {
        daysSold++;
      } else {
        daysNoSales++;
      }
    });
    
    // Calculate return rate
    const returnRate = totalSales > 0 ? ((totalReturns / totalSales) * 100).toFixed(1) : '0.0';
    
    // Calculate conversion days percentage
    const totalDays = daysSold + daysNoSales;
    const conversionDaysPercentage = totalDays > 0 ? ((daysSold / totalDays) * 100).toFixed(1) : '0.0';
    
    return {
      'units-sold': totalSales.toLocaleString(),
      'royalties': `$${totalRoyalties.toLocaleString()}`,
      'units-returned': totalReturns.toLocaleString(),
      'return-rate': `${returnRate}%`,
      'days-sold': daysSold.toString(),
      'days-no-sales': daysNoSales.toString(),
      'conversion-days-percentage': `${conversionDaysPercentage}%`
    };
  }
  
  /**
   * Handle filter tab click
   */
  handleFilterTabClick(filterId) {
    const tabElement = this.containerElement.querySelector(`[data-filter="${filterId}"]`);
    
    // Check if tab is disabled
    if (tabElement && tabElement.classList.contains('disabled')) {
      // Show brief feedback for disabled tab
      this.showTabDisabledFeedback(tabElement);
      return;
    }
    
    // Check if tab is already active
    if (tabElement && tabElement.classList.contains('active')) {
      return; // No action needed if already active
    }
    
    // Clear all active tabs
    this.clearAllActiveTabs();
    
    // Set this tab as active
    if (tabElement) {
      tabElement.classList.add('active');
    }
    
    // Apply filter with tab-initiated flag
    this.isTabInitiated = true;
    this.applyDateFilter(filterId);
    this.isTabInitiated = false;
  }
  
  /**
   * Apply date filter based on selected tab
   */
  applyDateFilter(filterId) {
    if (!this.options.allTimeData || this.options.allTimeData.length === 0) {
      console.warn('No all-time data available for filtering');
      return;
    }
    
    const dateRange = this.calculateTabDateRange(filterId);
    if (!dateRange) {
      console.warn(`Invalid filter ID: ${filterId}`);
      return;
    }
    
    // Set period tracking for tab-based filtering
    this.currentPeriodType = 'tab';
    this.currentPeriodId = filterId;
    this.currentPeriodDuration = Math.ceil((dateRange.endDate.getTime() - dateRange.startDate.getTime()) / (24 * 60 * 60 * 1000));
    
    // Set label drill-down flag to false for tab-based filtering
    this.isLabelDrillDown = false;
    
    // Set tab-initiated flag to bypass minimum width enforcement
    this.isTabInitiatedFilter = true;
    
    // Filter data and update chart
    this.filterDailySalesHistoryToTimeframe(dateRange.startDate, dateRange.endDate, this.getFilterLabel(filterId));
    this.render();
    
    // Reset tab-initiated flag after filtering
    this.isTabInitiatedFilter = false;
  }
  

  
  /**
   * Calculate date range for a specific tab based on today's date
   */
  calculateTabDateRange(filterId) {
    const today = new Date();
    let startDate, endDate;
    
    switch (filterId) {
      case 'last-7-days':
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 7);
        endDate = new Date(today);
        break;
      case 'last-14-days':
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 14);
        endDate = new Date(today);
        break;
      case 'last-30-days':
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 30);
        endDate = new Date(today);
        break;
      case 'current-month':
        startDate = new Date(today.getFullYear(), today.getMonth(), 1);
        endDate = new Date(today);
        break;
      case 'previous-month':
        startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        endDate = new Date(today.getFullYear(), today.getMonth(), 0);
        break;
      case 'last-6-months':
        startDate = new Date(today);
        startDate.setMonth(today.getMonth() - 6);
        endDate = new Date(today);
        break;
      case 'current-year':
        startDate = new Date(today.getFullYear(), 0, 1);
        endDate = new Date(today);
        break;
      case 'all-times':
        // Use full data range
        const allTimeData = this.options.allTimeData;
        if (allTimeData && allTimeData.length > 0) {
          startDate = new Date(allTimeData[0].dateObj);
          endDate = new Date(allTimeData[allTimeData.length - 1].dateObj);
        } else {
          return null;
        }
        break;
      default:
        return null;
    }
    
    return { startDate, endDate };
  }
  
  /**
   * Check if we have sufficient data for a date range
   * This checks for actual data density, not just date range coverage
   */
  checkDataAvailability(startDate, endDate) {
    if (!this.options.allTimeData || this.options.allTimeData.length === 0) {
      return false;
    }
    
    const dataStartDate = new Date(this.options.allTimeData[0].dateObj);
    const dataEndDate = new Date(this.options.allTimeData[this.options.allTimeData.length - 1].dateObj);
    
    // First check if our data range covers the requested period
    if (dataStartDate > startDate || dataEndDate < endDate) {
      return false;
    }
    
    // Calculate the requested period duration in days
    const requestedDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000)) + 1;
    
    // Count how many days we actually have data for within the requested period
    const dataInPeriod = this.options.allTimeData.filter(d => {
      const date = new Date(d.dateObj);
      return date >= startDate && date <= endDate;
    });
    
    // Calculate data density (percentage of days with data)
    const dataDensity = dataInPeriod.length / requestedDays;
    
    // Different thresholds for different period types
    let requiredDensity = 0.5; // Default: need at least 50% of days
    
    if (requestedDays === 1) {
      // For single day periods (Today), we need 100% coverage
      requiredDensity = 1.0;
    } else if (requestedDays <= 7) {
      // For short periods (7 days), we need at least 70% coverage
      requiredDensity = 0.7;
    } else if (requestedDays <= 30) {
      // For medium periods (30 days), we need at least 50% coverage
      requiredDensity = 0.5;
    } else if (requestedDays <= 90) {
      // For longer periods (90 days), we need at least 30% coverage
      requiredDensity = 0.3;
    } else {
      // For very long periods (year, all-time), we need at least 20% coverage
      requiredDensity = 0.2;
    }
    
    // Also ensure we have at least a minimum number of data points
    const minDataPoints = Math.min(requestedDays, Math.max(1, Math.ceil(requestedDays * 0.1)));
    
    return dataDensity >= requiredDensity && dataInPeriod.length >= minDataPoints;
  }
  
  /**
   * Update tab states based on data availability
   */
  updateTabStates() {
    if (this.type !== 'daily-sales-history') return;
    
    const tabs = this.containerElement.querySelectorAll('.snap-chart-filter-tab');
    if (tabs.length === 0) return;
    
    const tabIds = ['last-7-days', 'last-14-days', 'last-30-days', 'current-month', 'previous-month', 'last-6-months', 'current-year', 'all-times'];
    
    tabIds.forEach(tabId => {
      const tabElement = this.containerElement.querySelector(`[data-filter="${tabId}"]`);
      if (!tabElement) return;
      
      // Special handling for "All Time" tab - always enabled if we have any data
      if (tabId === 'all-times') {
        if (this.options.allTimeData && this.options.allTimeData.length > 0) {
          tabElement.classList.remove('disabled');
        } else {
          tabElement.classList.add('disabled');
        }
        return;
      }
      
      const dateRange = this.calculateTabDateRange(tabId);
      if (!dateRange) {
        tabElement.classList.add('disabled');
        return;
      }
      
      const hasData = this.checkDataAvailability(dateRange.startDate, dateRange.endDate);
      
      if (hasData) {
        tabElement.classList.remove('disabled');
      } else {
        tabElement.classList.add('disabled');
      }
    });
  }
  
  /**
   * Activate the default tab (Current Month) if no tab is currently active
   */
  activateDefaultTab() {
    if (this.type !== 'daily-sales-history') return;
    
    // Check if any tab is already active
    const activeTab = this.containerElement.querySelector('.snap-chart-filter-tab.active');
    if (activeTab) return; // Don't override if a tab is already active
    
    // Try to activate Current Month tab by default
    const currentMonthTab = this.containerElement.querySelector('[data-filter="current-month"]');
    if (currentMonthTab && !currentMonthTab.classList.contains('disabled')) {
      // Activate Current Month tab
      currentMonthTab.classList.add('active');
      
      // Apply the Current Month filter
      this.isTabInitiated = true;
      this.applyDateFilter('current-month');
      this.isTabInitiated = false;
    } else {
      // If Current Month is not available, try other tabs in order of preference
      const fallbackTabs = ['last-30-days', 'last-14-days', 'last-7-days', 'current-year', 'all-times'];
      
      for (const tabId of fallbackTabs) {
        const tabElement = this.containerElement.querySelector(`[data-filter="${tabId}"]`);
        if (tabElement && !tabElement.classList.contains('disabled')) {
          tabElement.classList.add('active');
          
          // Apply the fallback filter
          this.isTabInitiated = true;
          this.applyDateFilter(tabId);
          this.isTabInitiated = false;
          break;
        }
      }
    }
  }
  
  /**
   * Clear all active tabs
   */
  clearAllActiveTabs() {
    const tabs = this.containerElement.querySelectorAll('.snap-chart-filter-tab');
    tabs.forEach(tab => {
      tab.classList.remove('active');
    });
  }
  
  /**
   * Check if slider range matches any tab and activate it
   */
  checkSliderTabMatch(sliderStart, sliderEnd) {
    if (this.isTabInitiated) return; // Don't check matches for tab-initiated changes
    
    const toleranceDays = 1; // 1 day tolerance for matching
    const tabIds = ['last-7-days', 'last-14-days', 'last-30-days', 'current-month', 'previous-month', 'last-6-months', 'current-year', 'all-times'];
    
    // If the change was initiated by a label click, preserve the currently active tab
    // instead of clearing all tabs and finding a new match
    if (this.isLabelInitiatedFilter || this.wasLabelInitiated || this.currentPeriodType === 'label') {
      // Find the currently active tab
      const activeTab = this.containerElement.querySelector('.snap-chart-filter-tab.active');
      if (activeTab) {
        // Keep the active tab active - don't change tab state for label-initiated changes
        return;
      }
    }
    
    // Clear all active tabs first (only for non-label-initiated changes)
    this.clearAllActiveTabs();
    
    // Check each tab for a match
    for (let tabId of tabIds) {
      const dateRange = this.calculateTabDateRange(tabId);
      if (!dateRange) continue;
      
      const tabElement = this.containerElement.querySelector(`[data-filter="${tabId}"]`);
      if (!tabElement || tabElement.classList.contains('disabled')) continue;
      
      // Check if dates match within tolerance
      if (this.datesMatch(sliderStart, dateRange.startDate, toleranceDays) && 
          this.datesMatch(sliderEnd, dateRange.endDate, toleranceDays)) {
        tabElement.classList.add('active');
        break; // Only activate the first match
      }
    }
  }
  
  /**
   * Check if two dates match within a tolerance
   */
  datesMatch(date1, date2, toleranceDays = 1) {
    const diffTime = Math.abs(date1 - date2);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays <= toleranceDays;
  }
  
  /**
   * Show feedback for disabled tab
   */
  showTabDisabledFeedback(tabElement) {
    // Create a temporary tooltip-like element
    const feedback = document.createElement('div');
    feedback.className = 'snap-chart-tab-feedback';
    feedback.textContent = 'Insufficient data for this period';
    feedback.style.cssText = `
      position: absolute;
      top: -30px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 11px;
      white-space: nowrap;
      z-index: 1000;
      pointer-events: none;
    `;
    
    tabElement.style.position = 'relative';
    tabElement.appendChild(feedback);
    
    // Remove feedback after 2 seconds
    setTimeout(() => {
      if (feedback.parentNode) {
        feedback.parentNode.removeChild(feedback);
      }
    }, 2000);
  }
  
  /**
   * Get filter label for subtitle
   */
  getFilterLabel(filterId) {
    const labels = {
      'last-7-days': 'Last 7 Days',
      'last-14-days': 'Last 14 Days',
      'last-30-days': 'Last 30 Days',
      'current-month': 'Current Month',
      'previous-month': 'Previous Month',
      'last-6-months': 'Last 6 Months',
      'current-year': 'Current Year',
      'all-times': 'Lifetime'
    };
    return labels[filterId] || 'Unknown Period';
  }
  
  /**
   * Create chart canvas area
   */
  createCanvas() {
    const canvas = document.createElement('div');
    canvas.className = 'snap-chart-canvas';
    
    // Add production mode class if no container wrapper
    if (!this.demoOptions.showContainer) {
      canvas.classList.add('production-mode');
    }
    
    canvas.setAttribute('role', 'img');
    canvas.setAttribute('aria-label', `${this.options.title || 'Chart'}: ${this.options.subtitle || 'Interactive chart'}`);
    
    // Create SVG element
    this.svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    this.svg.classList.add('snap-chart-svg');
    this.svg.setAttribute('viewBox', `0 0 ${this.getCanvasWidth()} ${this.options.height}`);
    this.svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');
    this.svg.setAttribute('role', 'graphics-document');
    this.svg.setAttribute('aria-labelledby', 'chart-title chart-desc');
    
    // Add accessibility title and description
    const title = document.createElementNS('http://www.w3.org/2000/svg', 'title');
    title.id = 'chart-title';
    title.textContent = this.options.title || 'Chart';
    this.svg.appendChild(title);
    
    const desc = document.createElementNS('http://www.w3.org/2000/svg', 'desc');
    desc.id = 'chart-desc';
    desc.textContent = this.generateAccessibilityDescription();
    this.svg.appendChild(desc);
    
    canvas.appendChild(this.svg);
    
    return canvas;
  }
  
  /**
   * Create tooltip element
   */
  createTooltip() {
    this.tooltip = document.createElement('div');
    this.tooltip.className = 'snap-chart-tooltip';
    this.tooltip.setAttribute('role', 'tooltip');
    this.tooltip.setAttribute('aria-live', 'polite');

    // Append tooltip to appropriate container for correct positioning context
    if (this.chartContainer) {
      // Demo mode: append to chart container
      this.chartContainer.appendChild(this.tooltip);
    } else {
      // Production mode: append to the main container element (which should have position: relative)
      // This ensures tooltip positioning works correctly in dashboard context
      this.containerElement.appendChild(this.tooltip);
    }
  }
  
  /**
   * Create data editor component
   */
  createDataEditor() {
    // Create data editor container
    this.dataEditor = document.createElement('div');
    this.dataEditor.className = 'snap-chart-data-editor collapsed';
    
    // Create header
    const header = document.createElement('div');
    header.className = 'snap-chart-data-editor-header';
    
    const title = document.createElement('h4');
    title.className = 'snap-chart-data-editor-title';
    title.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
      </svg>
      Data Editor
    `;
    
    const toggle = document.createElement('div');
    toggle.className = 'snap-chart-data-editor-toggle';
    toggle.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        <path d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
      </svg>
    `;
    
    header.appendChild(title);
    header.appendChild(toggle);
    
    // Create content
    const content = document.createElement('div');
    content.className = 'snap-chart-data-editor-content';
    
    // Create tabs
    const tabs = document.createElement('div');
    tabs.className = 'snap-chart-data-editor-tabs';
    
    const jsonTab = document.createElement('button');
    jsonTab.className = 'snap-chart-data-editor-tab active';
    jsonTab.textContent = 'JSON Editor';
    jsonTab.setAttribute('data-tab', 'json');
    
    const tableTab = document.createElement('button');
    tableTab.className = 'snap-chart-data-editor-tab';
    tableTab.textContent = 'Table Editor';
    tableTab.setAttribute('data-tab', 'table');
    
    tabs.appendChild(jsonTab);
    tabs.appendChild(tableTab);
    
    // Create JSON editor
    const jsonEditor = document.createElement('div');
    jsonEditor.className = 'snap-chart-data-editor-json active';
    jsonEditor.setAttribute('data-content', 'json');
    
    const textarea = document.createElement('textarea');
    textarea.className = 'snap-chart-data-editor-textarea';
    textarea.placeholder = 'Enter your chart data in JSON format...';
    
    // Load data asynchronously
    this.formatDataForDisplay().then(data => {
      textarea.value = data;
    }).catch(error => {
      console.error('Failed to load data for display:', error);
      textarea.value = '[]';
    });
    
    jsonEditor.appendChild(textarea);
    
    // Create table editor
    const tableEditor = document.createElement('div');
    tableEditor.className = 'snap-chart-data-editor-table';
    tableEditor.setAttribute('data-content', 'table');
    
    this.createTableEditor(tableEditor);
    
    // Create actions
    const actions = document.createElement('div');
    actions.className = 'snap-chart-data-editor-actions';
    
    const applyBtn = document.createElement('button');
    applyBtn.className = 'snap-chart-data-editor-btn primary';
    applyBtn.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
      </svg>
      Apply Changes
    `;
    
    const resetBtn = document.createElement('button');
    resetBtn.className = 'snap-chart-data-editor-btn';
    resetBtn.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        <path d="M12 5V1L7 6l5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6H4c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z"/>
      </svg>
      Reset to Default
    `;
    
    const addRowBtn = document.createElement('button');
    addRowBtn.className = 'snap-chart-data-editor-btn';
    addRowBtn.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
      </svg>
      Add Row
    `;
    
    const templateBtn = document.createElement('button');
    templateBtn.className = 'snap-chart-data-editor-btn';
    templateBtn.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.89 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm4 18H6V4h7v5h5v11z"/>
      </svg>
      Load Template
    `;
    
    actions.appendChild(applyBtn);
    actions.appendChild(resetBtn);
    actions.appendChild(addRowBtn);
    actions.appendChild(templateBtn);
    
    // Create status message
    const status = document.createElement('div');
    status.className = 'snap-chart-data-editor-status';
    status.style.display = 'none';
    
    // Create helper
    const helper = document.createElement('div');
    helper.className = 'snap-chart-data-editor-helper';
    
    const helperTitle = document.createElement('h5');
    helperTitle.className = 'snap-chart-data-editor-helper-title';
    helperTitle.textContent = 'Data Format Template:';
    
    const helperContent = document.createElement('pre');
    helperContent.className = 'snap-chart-data-editor-helper-content';
    
    // Load template asynchronously
    this.getDataTemplate().then(template => {
      helperContent.textContent = template;
    }).catch(error => {
      console.error('Failed to load template:', error);
      helperContent.textContent = 'Error loading template';
    });
    
    helper.appendChild(helperTitle);
    helper.appendChild(helperContent);
    
    // Assemble content
    content.appendChild(tabs);
    content.appendChild(jsonEditor);
    content.appendChild(tableEditor);
    content.appendChild(actions);
    content.appendChild(status);
    content.appendChild(helper);
    
    // Assemble editor
    this.dataEditor.appendChild(header);
    this.dataEditor.appendChild(content);
    
    // Add to container (positioned below chart container)
    this.containerElement.appendChild(this.dataEditor);
    
    // Store references
    this.dataEditorElements = {
      header,
      content,
      tabs,
      jsonTab,
      tableTab,
      jsonEditor,
      tableEditor,
      textarea,
      actions,
      applyBtn,
      resetBtn,
      addRowBtn,
      templateBtn,
      status,
      helper,
      helperContent
    };
    
    // Initialize event listeners
    this.initializeDataEditor();
  }
  
  /**
   * Initialize data editor event listeners
   */
  initializeDataEditor() {
    const elements = this.dataEditorElements;
    
    // Header toggle
    elements.header.addEventListener('click', () => {
      this.dataEditor.classList.toggle('collapsed');
    });
    
    // Tab switching
    elements.jsonTab.addEventListener('click', () => this.switchEditorTab('json'));
    elements.tableTab.addEventListener('click', () => this.switchEditorTab('table'));
    
    // Action buttons
    elements.applyBtn.addEventListener('click', () => this.applyDataChanges());
    elements.resetBtn.addEventListener('click', async () => await this.resetToDefaultData());
    elements.addRowBtn.addEventListener('click', () => this.addDataRow());
    elements.templateBtn.addEventListener('click', async () => await this.loadDataTemplate());
    
    // Real-time validation on textarea input
    elements.textarea.addEventListener('input', () => {
      this.debounce(() => this.validateCurrentData(), 500);
    });
    
    // Auto-sync between JSON and table views
    elements.textarea.addEventListener('blur', () => {
      this.syncTableFromJSON();
    });
  }
  
  /**
   * Switch between JSON and table editor tabs
   */
  switchEditorTab(tab) {
    const elements = this.dataEditorElements;
    
    // Update tab buttons
    elements.jsonTab.classList.toggle('active', tab === 'json');
    elements.tableTab.classList.toggle('active', tab === 'table');
    
    // Update content panels
    elements.jsonEditor.classList.toggle('active', tab === 'json');
    elements.tableEditor.classList.toggle('active', tab === 'table');
    
    // Sync data between views
    if (tab === 'table') {
      this.syncTableFromJSON();
    } else {
      this.syncJSONFromTable();
    }
  }
  
  /**
   * Create table editor interface
   */
  createTableEditor(container) {
    const table = document.createElement('table');
    table.className = 'snap-chart-data-table';
    
    // Create table based on chart type
    this.populateTable(table);
    
    container.appendChild(table);
    this.dataTable = table;
  }
  
  /**
   * Populate table with current data
   */
  populateTable(table) {
    table.innerHTML = '';
    
    if (!this.data || this.data.length === 0) {
      const row = table.insertRow();
      const cell = row.insertCell();
      cell.colSpan = 10;
      cell.textContent = 'No data available. Click "Load Template" to get started.';
      cell.style.textAlign = 'center';
      cell.style.padding = '20px';
      return;
    }
    
    // Create header row
    const headerRow = table.insertRow();
    const columns = this.getTableColumns();
    
    columns.forEach(column => {
      const th = document.createElement('th');
      th.textContent = column.label;
      th.title = column.description || column.label;
      headerRow.appendChild(th);
    });
    
    // Add actions column
    const actionsHeader = document.createElement('th');
    actionsHeader.textContent = 'Actions';
    actionsHeader.style.width = '80px';
    headerRow.appendChild(actionsHeader);
    
    // Create data rows
    this.data.forEach((dataPoint, index) => {
      this.createTableRow(table, dataPoint, index, columns);
    });
  }
  
  /**
   * Create a single table row
   */
  createTableRow(table, dataPoint, index, columns) {
    const row = table.insertRow();
    row.setAttribute('data-index', index);
    
    columns.forEach(column => {
      const cell = row.insertCell();
      
      if (column.readonly) {
        // Create a readonly display element
        const display = document.createElement('div');
        display.style.padding = '4px 8px';
        display.style.color = 'var(--text-secondary)';
        display.style.fontStyle = 'italic';
        display.style.fontSize = '11px';
        display.style.minHeight = '20px';
        display.style.display = 'flex';
        display.style.alignItems = 'center';
        
        const value = this.getValueFromDataPoint(dataPoint, column.key);
        if (column.type === 'json') {
          display.style.fontFamily = 'monospace';
          display.style.whiteSpace = 'pre-wrap';
          display.style.fontSize = '10px';
          display.style.maxHeight = '100px';
          display.style.overflow = 'auto';
        }
        display.textContent = value || 'Auto-calculated';
        display.title = column.description || 'This field is automatically calculated';
        
        cell.appendChild(display);
      } else {
        // Create editable input
        const input = document.createElement('input');
        input.type = column.type === 'json' ? 'text' : (column.type || 'text');
        input.value = this.getValueFromDataPoint(dataPoint, column.key) || '';
        input.setAttribute('data-key', column.key);
        input.addEventListener('input', () => this.handleTableCellChange(index, column.key, input.value));
        
        if (column.type === 'number') {
          input.step = 'any';
          input.min = column.min || 0;
        }
        
        if (column.type === 'json') {
          input.style.fontFamily = 'monospace';
          input.style.fontSize = '11px';
        }
        
        cell.appendChild(input);
      }
    });
    
    // Add actions cell
    const actionsCell = row.insertCell();
    actionsCell.className = 'snap-chart-data-table-row-actions';
    
    const deleteBtn = document.createElement('button');
    deleteBtn.className = 'snap-chart-data-table-row-btn danger';
    deleteBtn.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
        <path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/>
      </svg>
    `;
    deleteBtn.title = 'Delete row';
    deleteBtn.addEventListener('click', () => this.deleteTableRow(index));
    
    actionsCell.appendChild(deleteBtn);
  }
  
  /**
   * Get table columns based on chart type
   */
  getTableColumns() {
    switch (this.type) {
      case 'daily-sales-history':
        return [
          { key: 'month', label: 'Month', type: 'text', description: 'Month abbreviation (MMM)' },
          { key: 'day', label: 'Day', type: 'text', description: 'Day of month (DD)' },
          { key: 'year', label: 'Year', type: 'text', description: 'Year (YY)' },
          { key: 'sales', label: 'Sales', type: 'number', min: 0, description: 'Sales amount' },
          { key: 'royalties', label: 'Royalties', type: 'number', min: 0, description: 'Royalties amount' },
          { key: 'returns', label: 'Returns', type: 'number', min: 0, description: 'Returns amount' }
        ];
      
      case 'stacked-column':
        return [
          { key: 'month', label: 'Month', type: 'text', description: 'Month abbreviation (MMM)' },
          { key: 'day', label: 'Day', type: 'text', description: 'Day of month (DD)' },
          { key: 'year', label: 'Year', type: 'text', description: 'Year (YY)' },
          { key: 'sales', label: 'Total Sales', type: 'number', min: 0, description: 'Total sales amount (calculated from marketplaces)', readonly: true },
          { key: 'royalties', label: 'Total Royalties', type: 'number', min: 0, description: 'Total royalties amount (calculated from marketplaces)', readonly: true },
          { key: 'returns', label: 'Total Returns', type: 'number', min: 0, description: 'Total returns amount (calculated from marketplaces)', readonly: true },
          { key: 'marketplaces', label: 'Marketplaces', type: 'json', description: 'Array of marketplace objects with code, sales, royalties, returns' },
          { key: 'values', label: 'Legacy Values', type: 'text', description: 'Comma-separated values (backward compatibility)', readonly: true },
          { key: 'labels', label: 'Legacy Labels', type: 'text', description: 'Comma-separated labels (backward compatibility)', readonly: true }
        ];
      
      case 'scrollable-stacked-column':
        return [
          { key: 'month', label: 'Month', type: 'text', description: 'Month abbreviation (MMM)' },
          { key: 'year', label: 'Year', type: 'text', description: 'Year (YY)' },
          { key: 'sales', label: 'Total Sales', type: 'number', min: 0, description: 'Total sales amount (calculated from marketplaces)', readonly: true },
          { key: 'royalties', label: 'Total Royalties', type: 'number', min: 0, description: 'Total royalties amount (calculated from marketplaces)', readonly: true },
          { key: 'returns', label: 'Total Returns', type: 'number', min: 0, description: 'Total returns amount (calculated from marketplaces)', readonly: true },
          { key: 'marketplaces', label: 'Marketplaces', type: 'json', description: 'Array of marketplace objects with code, sales, royalties, returns' },
          { key: 'values', label: 'Legacy Values', type: 'text', description: 'Comma-separated values (backward compatibility)', readonly: true },
          { key: 'labels', label: 'Legacy Labels', type: 'text', description: 'Comma-separated labels (backward compatibility)', readonly: true }
        ];
      
      case 'pie':
        return [
          { key: 'label', label: 'Label', type: 'text', description: 'Pie slice label' },
          { key: 'value', label: 'Value', type: 'number', min: 0, description: 'Pie slice value' },
          { key: 'percentage', label: 'Percentage', type: 'number', min: 0, max: 100, description: 'Pie slice percentage (optional)' }
        ];
      
      default:
        return [
          { key: 'month', label: 'Month', type: 'text' },
          { key: 'day', label: 'Day', type: 'text' },
          { key: 'sales', label: 'Sales', type: 'number', min: 0 },
          { key: 'royalties', label: 'Royalties', type: 'number', min: 0 }
        ];
    }
  }
  
  /**
   * Get value from data point for table display
   */
  getValueFromDataPoint(dataPoint, key) {
    if (!dataPoint) return '';
    
    const value = dataPoint[key];
    if (value === undefined || value === null) return '';
    
    // Handle special array fields
    if (key === 'values' && Array.isArray(value)) {
      return value.join(', ');
    }
    if (key === 'labels' && Array.isArray(value)) {
      return value.join(', ');
    }
    
    // Handle marketplace data as JSON
    if (key === 'marketplaces') {
      return Array.isArray(value) ? JSON.stringify(value, null, 2) : value;
    }
    
    return value;
  }
  
  /**
   * Handle table cell value change
   */
  handleTableCellChange(index, key, value) {
    if (!this.data[index]) return;
    
    // Skip readonly fields (they are calculated)
    const columns = this.getTableColumns();
    const column = columns.find(col => col.key === key);
    if (column && column.readonly) {
      return;
    }
    
    // Parse special fields
    if (key === 'values') {
      this.data[index][key] = value.split(',').map(v => parseFloat(v.trim())).filter(v => !isNaN(v));
    } else if (key === 'labels') {
      this.data[index][key] = value.split(',').map(v => v.trim()).filter(v => v);
    } else if (key === 'marketplaces') {
      try {
        this.data[index][key] = JSON.parse(value);
      } catch (error) {
        console.warn('Invalid JSON for marketplaces:', error);
        return;
      }
    } else if (key === 'sales' || key === 'royalties' || key === 'returns') {
      this.data[index][key] = parseFloat(value) || 0;
    } else {
      this.data[index][key] = value;
    }
    
    // Reprocess the data to recalculate totals if marketplace data was changed
    if (key === 'marketplaces') {
      this.data[index] = this.processMarketplaceData(this.data[index]);
      // Update the table display to show recalculated values
      this.populateTable(this.dataEditorElements.table);
    }
    
    // Update JSON view
    this.formatDataForDisplay().then(data => {
      this.dataEditorElements.textarea.value = data;
    }).catch(error => {
      console.error('Failed to format data for display:', error);
    });
    
    // Validate and potentially update chart
    this.debounce(() => {
      if (this.validateCurrentData()) {
        this.updateChartFromEditor();
      }
    }, 1000);
  }
  
  /**
   * Delete a table row
   */
  deleteTableRow(index) {
    if (this.data && this.data.length > index) {
      this.data.splice(index, 1);
      this.populateTable(this.dataTable);
      this.formatDataForDisplay().then(data => {
        this.dataEditorElements.textarea.value = data;
      }).catch(error => {
        console.error('Failed to format data for display:', error);
      });
      this.updateChartFromEditor();
    }
  }
  
  /**
   * Add a new data row
   */
  addDataRow() {
    const newRow = this.createEmptyDataPoint();
    this.data.push(newRow);
    this.populateTable(this.dataTable);
    this.formatDataForDisplay().then(data => {
      this.dataEditorElements.textarea.value = data;
    }).catch(error => {
      console.error('Failed to format data for display:', error);
    });
    this.showStatus('New row added. Fill in the data and click Apply Changes.', 'success');
  }
  
  /**
   * Create empty data point based on chart type
   */
  createEmptyDataPoint() {
    if (this.type === 'daily-sales-history') {
      return {
        month: "MMM",
        day: "DD",
        year: "YY",
        sales: 0,
        royalties: 0,
        returns: 0,
        fullDate: "YYYY-MM-DD",
        dateObj: "YYYY-MM-DDTHH:mm:ss.sssZ"
      };
    }
    
    if (this.type === 'stacked-column') {
      return {
        month: "MMM",
        day: "DD",
        year: "YY",
        marketplaces: [
          { code: "US", sales: 0, royalties: 0, returns: 0 },
          { code: "UK", sales: 0, royalties: 0, returns: 0 },
          { code: "DE", sales: 0, royalties: 0, returns: 0 }
        ],
        sales: 0,
        royalties: 0,
        returns: 0,
        values: [0, 0, 0],
        labels: ["US", "UK", "DE"]
      };
    }
    
    if (this.type === 'scrollable-stacked-column') {
      return {
        month: "MMM",
        year: "YY",
        marketplaces: [
          { code: "US", sales: 0, royalties: 0, returns: 0 },
          { code: "UK", sales: 0, royalties: 0, returns: 0 },
          { code: "DE", sales: 0, royalties: 0, returns: 0 }
        ],
        sales: 0,
        royalties: 0,
        returns: 0,
        values: [0, 0, 0],
        labels: ["US", "UK", "DE"]
      };
    }
    
    if (this.type === 'pie') {
      return {
        label: "New Label",
        value: 0,
        percentage: 0
      };
    }
    
    // Default structure (fallback)
    return {
      month: "MMM",
      day: "DD",
      sales: 0,
      royalties: 0
    };
  }
  
  /**
   * Format current data for display in textarea
   */
  async formatDataForDisplay() {
    if (!this.data || this.data.length === 0) {
      return await this.getDataTemplate();
    }
    
    try {
      return JSON.stringify(this.data, null, 2);
    } catch (error) {
      return await this.getDataTemplate();
    }
  }
  
  /**
   * Get data template based on chart type
   */
  async getDataTemplate() {
    if (window.SnapDataLoader) {
      try {
        return await window.SnapDataLoader.getTemplateString(this.type);
      } catch (error) {
        console.warn('Failed to load template from external source, falling back to default:', error);
      }
    }
    
    // Fallback to basic structure templates (no hardcoded sample data)
    switch (this.type) {
      case 'daily-sales-history':
        return `[
  {
    "month": "MMM",
    "day": "DD",
    "year": "YY",
    "sales": 0,
    "royalties": 0,
    "returns": 0,
    "fullDate": "YYYY-MM-DD",
    "dateObj": "YYYY-MM-DDTHH:mm:ss.sssZ"
  }
]`;
      
      case 'stacked-column':
        return `[
  {
    "month": "MMM",
    "day": "DD",
    "year": "YY",
    "marketplaces": [
      {
        "code": "US",
        "sales": 0,
        "royalties": 0,
        "returns": 0
      },
      {
        "code": "UK",
        "sales": 0,
        "royalties": 0,
        "returns": 0
      }
    ],
    "sales": 0,
    "royalties": 0,
    "returns": 0,
    "values": [],
    "labels": []
  }
]`;
      
      case 'scrollable-stacked-column':
        return `[
  {
    "month": "MMM",
    "year": "YY",
    "marketplaces": [
      {
        "code": "US",
        "sales": 0,
        "royalties": 0,
        "returns": 0
      },
      {
        "code": "UK",
        "sales": 0,
        "royalties": 0,
        "returns": 0
      }
    ],
    "sales": 0,
    "royalties": 0,
    "returns": 0,
    "values": [],
    "labels": []
  }
]`;
      
      case 'pie':
        return `[
  {
    "label": "string",
    "value": 0,
    "percentage": 0
  }
]`;
      
      default:
        return `[
  {
    "month": "MMM",
    "day": "DD",
    "sales": 0,
    "royalties": 0
  }
]`;
    }
  }
  
  /**
   * Validate current data in editor
   */
  validateCurrentData() {
    try {
      const jsonData = this.dataEditorElements.textarea.value.trim();
      if (!jsonData) {
        this.showStatus('Data cannot be empty.', 'error');
        return false;
      }
      
      const parsedData = JSON.parse(jsonData);
      
      if (!Array.isArray(parsedData)) {
        this.showStatus('Data must be an array of objects.', 'error');
        return false;
      }
      
      if (parsedData.length === 0) {
        this.showStatus('Data array cannot be empty.', 'error');
        return false;
      }
      
      // Validate each data point
      for (let i = 0; i < parsedData.length; i++) {
        const point = parsedData[i];
        const validation = this.validateDataPoint(point, i);
        if (!validation.valid) {
          this.showStatus(`Row ${i + 1}: ${validation.error}`, 'error');
          return false;
        }
      }
      
      this.showStatus('Data is valid!', 'success');
      return true;
    } catch (error) {
      this.showStatus(`JSON parsing error: ${error.message}`, 'error');
      return false;
    }
  }
  
  /**
   * Validate individual data point
   */
  validateDataPoint(point, index) {
    if (!point || typeof point !== 'object') {
      return { valid: false, error: 'Must be an object' };
    }
    
    // Required fields based on chart type
    let requiredFields = [];
    
    if (this.type === 'daily-sales-history') {
      requiredFields = ['month', 'day', 'year', 'sales', 'royalties', 'returns'];
    } else if (this.type === 'stacked-column') {
      requiredFields = ['month', 'day', 'year', 'sales', 'royalties', 'values', 'labels'];
    } else if (this.type === 'scrollable-stacked-column') {
      requiredFields = ['month', 'year', 'sales', 'royalties', 'values', 'labels'];
    } else if (this.type === 'pie') {
      requiredFields = ['label', 'value'];
    } else {
      requiredFields = ['month', 'day', 'sales', 'royalties'];
    }
    
    // Check required fields
    for (const field of requiredFields) {
      if (!(field in point)) {
        return { valid: false, error: `Missing required field: ${field}` };
      }
    }
    
    // Validate field types and values based on chart type
    if (this.type === 'pie') {
      // Pie chart specific validation
      if (typeof point.label !== 'string' || !point.label.trim()) {
        return { valid: false, error: 'Label must be a non-empty string' };
      }
      
      if (typeof point.value !== 'number' || point.value < 0) {
        return { valid: false, error: 'Value must be a non-negative number' };
      }
      
      // Percentage is optional but if present must be valid
      if ('percentage' in point && (typeof point.percentage !== 'number' || point.percentage < 0 || point.percentage > 100)) {
        return { valid: false, error: 'Percentage must be a number between 0 and 100' };
      }
    } else {
      // Validation for other chart types
      if (typeof point.month !== 'string' || !point.month.trim()) {
        return { valid: false, error: 'Month must be a non-empty string' };
      }
      
      // Only validate day field for charts that use it
      if ('day' in point && (typeof point.day !== 'string' || !point.day.trim())) {
        return { valid: false, error: 'Day must be a non-empty string' };
      }
      
      // Validate year field for charts that use it
      if ('year' in point && (typeof point.year !== 'string' || !point.year.trim())) {
        return { valid: false, error: 'Year must be a non-empty string' };
      }
      
      if ('sales' in point && (typeof point.sales !== 'number' || point.sales < 0)) {
        return { valid: false, error: 'Sales must be a non-negative number' };
      }
      
      if ('royalties' in point && (typeof point.royalties !== 'number' || point.royalties < 0)) {
        return { valid: false, error: 'Royalties must be a non-negative number' };
      }
      
      if ('returns' in point && (typeof point.returns !== 'number' || point.returns < 0)) {
        return { valid: false, error: 'Returns must be a non-negative number' };
      }
    }
    
    // Validate stacked column specific fields
    if (this.type === 'stacked-column' || this.type === 'scrollable-stacked-column') {
      // Check if we have new marketplace structure
      if (point.marketplaces && Array.isArray(point.marketplaces)) {
        // Validate marketplace structure
        if (point.marketplaces.length === 0) {
          return { valid: false, error: 'Marketplaces array cannot be empty' };
        }
        
        for (let i = 0; i < point.marketplaces.length; i++) {
          const marketplace = point.marketplaces[i];
          
          if (!marketplace.code || typeof marketplace.code !== 'string') {
            return { valid: false, error: `Marketplace at index ${i} must have a valid code` };
          }
          
          if (typeof marketplace.sales !== 'number' || marketplace.sales < 0) {
            return { valid: false, error: `Marketplace ${marketplace.code} sales must be a non-negative number` };
          }
          
          if (typeof marketplace.royalties !== 'number' || marketplace.royalties < 0) {
            return { valid: false, error: `Marketplace ${marketplace.code} royalties must be a non-negative number` };
          }
          
          if (typeof marketplace.returns !== 'number' || marketplace.returns < 0) {
            return { valid: false, error: `Marketplace ${marketplace.code} returns must be a non-negative number` };
          }
        }
      } 
      // Fallback validation for legacy values/labels structure
      else if (point.values && point.labels) {
        if (!Array.isArray(point.values)) {
          return { valid: false, error: 'Values must be an array' };
        }
        
        if (!Array.isArray(point.labels)) {
          return { valid: false, error: 'Labels must be an array' };
        }
        
        if (point.values.length !== point.labels.length) {
          return { valid: false, error: 'Values and labels arrays must have the same length' };
        }
        
        for (let i = 0; i < point.values.length; i++) {
          if (typeof point.values[i] !== 'number' || point.values[i] < 0) {
            return { valid: false, error: `Value at index ${i} must be a non-negative number` };
          }
        }
      } else {
        return { valid: false, error: 'Stacked column data must have either marketplaces array or values/labels arrays' };
      }
    }
    
    return { valid: true };
  }
  
  /**
   * Apply data changes to chart
   */
  applyDataChanges() {
    if (!this.validateCurrentData()) {
      return;
    }
    
    try {
      const newData = JSON.parse(this.dataEditorElements.textarea.value);
      this.updateChartFromEditor(newData);
      this.showStatus('Chart updated successfully!', 'success');
    } catch (error) {
      this.showStatus(`Error applying changes: ${error.message}`, 'error');
    }
  }
  
  /**
   * Update chart with new data from editor
   */
  updateChartFromEditor(newData = null) {
    if (newData) {
      // Process the data based on chart type
      const processedData = this.processEditorData(newData);
      this.updateData(processedData);
    } else {
      // Re-render with current data
      this.render();
    }
  }
  
  /**
   * Process editor data for chart consumption
   */
  processEditorData(data) {
    return data.map(point => {
      let processed = { ...point };
      
      // For daily sales history, ensure dateObj is a Date object
      if (this.type === 'daily-sales-history' && point.fullDate) {
        processed.dateObj = new Date(point.fullDate);
      }
      
      // Process marketplace data structure for stacked columns
      if (this.type === 'stacked-column' || this.type === 'scrollable-stacked-column') {
        processed = this.processMarketplaceData(processed);
      }
      
      // For stacked columns, ensure values are numbers (backward compatibility)
      if (processed.values && Array.isArray(processed.values)) {
        processed.values = processed.values.map(v => parseFloat(v) || 0);
      }
      
      // Ensure numeric fields are numbers
      if ('sales' in processed) processed.sales = parseFloat(processed.sales) || 0;
      if ('royalties' in processed) processed.royalties = parseFloat(processed.royalties) || 0;
      if ('returns' in processed) processed.returns = parseFloat(processed.returns) || 0;
      if ('change' in processed) processed.change = parseFloat(processed.change) || 0;
      
      return processed;
    });
  }

  /**
   * Process marketplace data and calculate totals
   * Supports both new marketplace structure and legacy values/labels structure
   */
  processMarketplaceData(dataPoint) {
    const processed = { ...dataPoint };
    
    // If we have marketplace data, calculate totals and create backward compatibility arrays
    if (processed.marketplaces && Array.isArray(processed.marketplaces) && processed.marketplaces.length > 0) {
      // Calculate totals from individual marketplace data
      let totalSales = 0;
      let totalRoyalties = 0;
      let totalReturns = 0;
      const values = [];
      const labels = [];
      
      processed.marketplaces.forEach(marketplace => {
        const sales = parseFloat(marketplace.sales) || 0;
        const royalties = parseFloat(marketplace.royalties) || 0;
        const returns = parseFloat(marketplace.returns) || 0;
        
        totalSales += sales;
        totalRoyalties += royalties;
        totalReturns += returns;
        
        values.push(sales);
        labels.push(marketplace.code || 'Unknown');
      });
      
      // Set calculated totals
      processed.sales = totalSales;
      processed.royalties = totalRoyalties;
      processed.returns = totalReturns;
      processed.change = totalReturns; // For backward compatibility
      
      // Set backward compatibility arrays
      processed.values = values;
      processed.labels = labels;
    } 
    // If we have legacy values/labels structure, convert to marketplace structure
    else if (processed.values && Array.isArray(processed.values) && processed.labels && Array.isArray(processed.labels)) {
      const marketplaces = [];
      const totalSales = processed.sales || 0;
      const totalRoyalties = processed.royalties || 0;
      const totalReturns = processed.returns || processed.change || 0;
      
      processed.values.forEach((value, index) => {
        const marketplaceCode = processed.labels[index] || `Market${index + 1}`;
        const sales = parseFloat(value) || 0;
        
        // Calculate proportional royalties and returns (fallback for legacy data)
        const royalties = totalSales > 0 ? Math.round((sales / totalSales) * totalRoyalties * 100) / 100 : 0;
        
        // Handle returns - check if this marketplace should have 0 returns
        let returns = 0;
        if (processed.zeroReturnsMarketplaceIndex !== undefined && processed.zeroReturnsMarketplaceIndex === index) {
          returns = 0;
        } else if (totalSales > 0) {
          returns = Math.round((sales / totalSales) * totalReturns);
        }
        
        marketplaces.push({
          code: marketplaceCode,
          sales: sales,
          royalties: royalties,
          returns: returns
        });
      });
      
      processed.marketplaces = marketplaces;
      
      // Recalculate totals to ensure consistency - use the actual calculated values from marketplaces
      processed.sales = marketplaces.reduce((sum, m) => sum + m.sales, 0);
      processed.royalties = marketplaces.reduce((sum, m) => sum + m.royalties, 0);
      processed.returns = marketplaces.reduce((sum, m) => sum + m.returns, 0);
      processed.change = processed.returns; // For backward compatibility
      
      // Update the values array to reflect the actual marketplace sales
      processed.values = marketplaces.map(m => m.sales);
      processed.labels = marketplaces.map(m => m.code);
    }
    
    return processed;
  }
  
  /**
   * Reset to default data
   */
  async resetToDefaultData() {
    if (confirm('Are you sure you want to reset to default data? This will lose all your changes.')) {
      // Reset to original data if available, otherwise use template
      const defaultData = this.originalData || JSON.parse(await this.getDataTemplate());
      this.dataEditorElements.textarea.value = JSON.stringify(defaultData, null, 2);
      this.updateChartFromEditor(defaultData);
      this.populateTable(this.dataTable);
      this.showStatus('Reset to default data.', 'success');
    }
  }
  
  /**
   * Load data template
   */
  async loadDataTemplate() {
    this.dataEditorElements.textarea.value = await this.getDataTemplate();
    this.populateTable(this.dataTable);
    this.showStatus('Data template loaded. Please fill in your actual data.', 'warning');
  }
  
  /**
   * Sync table data from JSON
   */
  syncTableFromJSON() {
    try {
      const jsonData = JSON.parse(this.dataEditorElements.textarea.value);
      if (Array.isArray(jsonData)) {
        this.data = jsonData;
        this.populateTable(this.dataTable);
      }
    } catch (error) {
      // Invalid JSON, don't sync
    }
  }
  
  /**
   * Sync JSON from table data
   */
  syncJSONFromTable() {
    if (this.data) {
      this.formatDataForDisplay().then(data => {
        this.dataEditorElements.textarea.value = data;
      }).catch(error => {
        console.error('Failed to format data for display:', error);
      });
    }
  }
  
  /**
   * Show status message
   */
  showStatus(message, type = 'info') {
    const status = this.dataEditorElements.status;
    status.textContent = message;
    status.className = `snap-chart-data-editor-status ${type}`;
    status.style.display = 'block';
    
    // Auto-hide success messages
    if (type === 'success') {
      setTimeout(() => {
        status.style.display = 'none';
      }, 3000);
    }
  }
  
  /**
   * Debounce function for performance
   */
  debounce(func, wait) {
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    this.debounceTimer = setTimeout(func, wait);
  }
  
  /**
   * Generate accessibility description for screen readers
   */
  generateAccessibilityDescription() {
    if (!this.data || this.data.length === 0) {
      return 'No data available for this chart.';
    }
    
    const dataPoints = this.data.length;
    const maxSales = Math.max(...this.data.map(d => {
      const salesValue = d.sales || 0;
      const valuesSum = d.values ? d.values.reduce((sum, v) => sum + (v || 0), 0) : 0;
      return Math.max(salesValue, valuesSum);
    }));
    const maxRoyalties = Math.max(...this.data.map(d => d.royalties || 0));
    
    let description = `Stacked column chart showing sales by country marketplace with ${dataPoints} data points. `;
    description += `Sales range from 0 to ${maxSales} units. `;
    description += `Royalties range from $0 to $${maxRoyalties}. `;
    description += `Each column segment represents sales from different country marketplaces. `;
    
    if (this.options.compareMode) {
      description += 'Includes comparison data from previous period. ';
    }
    
    return description;
  }
  
  /**
   * Get canvas width based on container
   */
  getCanvasWidth() {
    if (this.options.width) return this.options.width;

    const containerWidth = this.containerElement.offsetWidth;

    // Use full container width for daily sales history and production mode to eliminate empty space
    if (this.type === 'daily-sales-history' || !this.demoOptions.showContainer) {
      return Math.max(containerWidth, 1100);
    }

    // For demo mode with container, apply minimal padding
    const padding = 24; // Reduced from 48px to 24px for better width utilization
    return Math.max(containerWidth - padding, 1100);
  }
  
  /**
   * Calculate time span in months between first and last data points
   */
  calculateTimeSpanInMonths() {
    if (!this.data || this.data.length === 0) {
      return 0;
    }
    
    // For daily sales history, use dateObj if available
    if (this.type === 'daily-sales-history' && this.data[0].dateObj && this.data[this.data.length - 1].dateObj) {
      const startDate = this.data[0].dateObj;
      const endDate = this.data[this.data.length - 1].dateObj;
      
      // Calculate months between dates
      const yearDiff = endDate.getFullYear() - startDate.getFullYear();
      const monthDiff = endDate.getMonth() - startDate.getMonth();
      return yearDiff * 12 + monthDiff + 1; // +1 to include both start and end months
    }
    
    // For other chart types, estimate based on data length and structure
    // If data has month/year info, try to calculate from that
    if (this.data[0].month && this.data[0].year && this.data[this.data.length - 1].month && this.data[this.data.length - 1].year) {
      const startYear = parseInt('20' + this.data[0].year);
      const endYear = parseInt('20' + this.data[this.data.length - 1].year);
      
      const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
      const startMonthIndex = monthNames.indexOf(this.data[0].month);
      const endMonthIndex = monthNames.indexOf(this.data[this.data.length - 1].month);
      
      if (startMonthIndex !== -1 && endMonthIndex !== -1) {
        const yearDiff = endYear - startYear;
        const monthDiff = endMonthIndex - startMonthIndex;
        return yearDiff * 12 + monthDiff + 1; // +1 to include both start and end months
      }
    }
    
    // Fallback: assume each data point represents one month
    return this.data.length;
  }

  /**
   * Determine if labels should show months or years based on time span
   */
  shouldShowMonthLabels() {
    const timeSpanInMonths = this.calculateTimeSpanInMonths();
    return timeSpanInMonths <= 18; // Show months for 18 months or less, years for longer periods
  }
  
  /**
   * Determine if labels should show only years (no quarters) for very long time spans
   */
  shouldShowOnlyYearLabels() {
    const timeSpanInMonths = this.calculateTimeSpanInMonths();
    return timeSpanInMonths > 96; // Show only years for 8+ years (96+ months)
  }
  
  /**
   * Determine if labels should show daily dates when zoomed in (30 columns or less)
   */
  shouldShowDailyLabels() {
    if (this.type !== 'daily-sales-history' || !this.data) return false;
    
    // Always show daily labels for 30 or fewer data points (original behavior)
    if (this.data.length <= 30) return true;
    
    // Also show daily labels when in label drill-down mode (up to 45 days to handle months + buffer)
    if (this.isLabelDrillDown && this.data.length <= 45) return true;
    
    // Show daily labels when slider is at minimum width (50px) and showing 7 days or less
    if (this.isSliderAtMinimumWidth()) return true;
    
    return false;
  }
  
  /**
   * Check if the slider is currently showing minimum width (7 days or less)
   */
  isSliderAtMinimumWidth() {
    if (!this.dateRangeControls || !this.options.currentStartDate || !this.options.currentEndDate) {
      return false;
    }
    
    // Calculate actual days in the current selection
    const currentTimeSpan = this.options.currentEndDate.getTime() - this.options.currentStartDate.getTime();
    const daysInSelection = Math.ceil(currentTimeSpan / (24 * 60 * 60 * 1000));
    
    // Check if selection is at or near minimum width (7 days)
    const isMinimumWidth = daysInSelection <= 7;
    
    return isMinimumWidth && !this.isYearFilter;
  }
  
  /**
   * Check if we have limited data that requires special handling
   */
  hasLimitedData() {
    if (!this.options.allTimeData || this.options.allTimeData.length === 0) {
      return { isLimited: true, reason: 'no-data', dataPoints: 0 };
    }
    
    const dataPoints = this.options.allTimeData.length;
    
    // Calculate the time span of our data
    const startDate = new Date(this.options.allTimeData[0].dateObj);
    const endDate = new Date(this.options.allTimeData[this.options.allTimeData.length - 1].dateObj);
    const timeSpanDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000)) + 1;
    
    // Check for various limited data scenarios
    if (dataPoints === 1) {
      return { isLimited: true, reason: 'single-day', dataPoints, timeSpanDays };
    } else if (dataPoints <= 3) {
      return { isLimited: true, reason: 'few-days', dataPoints, timeSpanDays };
    } else if (dataPoints <= 7 && timeSpanDays > 30) {
      return { isLimited: true, reason: 'sparse-data', dataPoints, timeSpanDays };
    } else if (timeSpanDays <= 7) {
      return { isLimited: true, reason: 'short-timespan', dataPoints, timeSpanDays };
    }
    
    return { isLimited: false, reason: 'sufficient-data', dataPoints, timeSpanDays };
  }
  
  /**
   * Get user-friendly message for limited data scenarios
   */
  getLimitedDataMessage() {
    const limitedInfo = this.hasLimitedData();
    
    if (!limitedInfo.isLimited) {
      return null;
    }
    
    switch (limitedInfo.reason) {
      case 'no-data':
        return 'No data available. Please add data to enable filtering options.';
      case 'single-day':
        return `Only 1 day of data available. Most time-based filters are disabled.`;
      case 'few-days':
        return `Only ${limitedInfo.dataPoints} days of data available. Some time-based filters may be disabled.`;
      case 'sparse-data':
        return `Limited data coverage (${limitedInfo.dataPoints} days over ${limitedInfo.timeSpanDays} days). Some filters may be disabled.`;
      case 'short-timespan':
        return `Data covers only ${limitedInfo.timeSpanDays} days. Long-term filters are disabled.`;
      default:
        return 'Limited data available. Some features may be disabled.';
    }
  }
  
  /**
   * Render the chart based on type
   */
  render() {
    // Cleanup Today vs Previous Years chart event listeners before re-rendering
    if (this.options.isTodayVsPreviousYearsChart) {
      this.cleanupTodayVsPreviousYearsChart();
    }

    // For pie charts, we don't use the SVG element, so skip SVG clearing
    if (this.type !== 'pie') {
      if (!this.svg) return;
      // Clear existing content for non-pie charts
      this.svg.innerHTML = '';
    }
    
    // Add animation class if enabled (but not for daily sales history due to performance)
    if (this.options.animate && this.type !== 'daily-sales-history') {
      this.containerElement.classList.add('snap-chart-animate-in');
      
      // Clean up will-change after animation completes
      setTimeout(() => {
        if (this.containerElement) {
          this.containerElement.classList.add('animation-complete');
        }
      }, 500); // Slightly longer than animation duration
    }
    
    // Render based on chart type
    switch (this.type) {
      case 'stacked-column':
        this.renderStackedColumn();
        break;
      case 'scrollable-stacked-column':
        this.renderScrollableStackedColumn();
        break;
      case 'daily-sales-history':
        this.renderDailySalesHistory();
        break;
      case 'pie':
        this.renderPieChart();
        break;
      default:
        console.warn(`SnapChart: Chart type "${this.type}" not implemented`);
    }
  }
  
  /**
   * Render stacked column chart
   */
  renderStackedColumn() {
    // Clear previous royalties points
    this.royaltiesPoints = [];
    
    if (!this.data || this.data.length === 0) {
      console.warn('SnapChart: No data provided for stacked column chart');
      return;
    }

    // Validate data structure to prevent runtime crashes
    const validatedData = this.data.filter(d => {
      if (!d) return false;
      // Ensure values array exists and is valid
      if (!d.values || !Array.isArray(d.values) || d.values.length === 0) {
        console.warn('SnapChart: Invalid data point - missing or empty values array', d);
        return false;
      }
      // Ensure all values are numbers
      if (!d.values.every(v => typeof v === 'number' && !isNaN(v))) {
        console.warn('SnapChart: Invalid data point - values must be numbers', d);
        return false;
      }
      return true;
    });

    if (validatedData.length === 0) {
      console.warn('SnapChart: No valid data points after validation');
      return;
    }

    // Use validated data for the rest of the function
    this.data = validatedData;



    const width = this.getCanvasWidth();
    const height = this.options.height;
    // Optimize padding for better width utilization in production mode
    const isProductionMode = !this.demoOptions.showContainer;
    const padding = {
      top: 60,
      right: isProductionMode ? 10 : 40, // Minimal right padding in production mode
      bottom: 40,
      left: isProductionMode ? 10 : 40   // Minimal left padding in production mode
    };
    const chartWidth = width - padding.left - padding.right;
    const chartHeight = height - padding.top - padding.bottom;

    // Keep grid insets at 48px for proper Y-axis label spacing (essential for readability)
    const gridInset = 48; // Keep 48px for proper spacing between labels and grid
    const gridWidth = chartWidth - (2 * gridInset);
    const gridStartX = gridInset;

    // --- UNIFIED COLUMN POSITIONING SYSTEM ---
    const N = this.data.length;
    const hasCompare = this.options.compareMode && this.options.compareData && this.options.compareData.length > 0;

    // Calculate scales for dual axis with safe data access
    let maxSales = Math.max(...this.data.map(d => {
      const salesValue = d.sales || 0;
      const valuesSum = d.values ? d.values.reduce((sum, v) => sum + (v || 0), 0) : 0;
      return Math.max(salesValue, valuesSum);
    }));
    let maxRoyalties = Math.max(...this.data.map(d => d.royalties || 0));
    
    // If compare mode is enabled and comparison data exists, also check comparison data for max values
    if (hasCompare && this.options.compareData && this.options.compareData.length > 0) {
      const maxComparisonSales = Math.max(...this.options.compareData.map(d => {
        const salesValue = d.sales || 0;
        const valuesSum = d.values ? d.values.reduce((sum, v) => sum + (v || 0), 0) : 0;
        return Math.max(salesValue, valuesSum);
      }));
      const maxComparisonRoyalties = Math.max(...this.options.compareData.map(d => d.royalties || 0));
      
      // Use the overall maximum between main and comparison data
      maxSales = Math.max(maxSales, maxComparisonSales);
      maxRoyalties = Math.max(maxRoyalties, maxComparisonRoyalties);
    }
    const salesScale = chartHeight / (maxSales || 1); // Prevent division by zero
    const royaltiesScale = chartHeight / (maxRoyalties || 1); // Prevent division by zero
    
    // Column dimensions
    const mainColumnWidth = 24; // Updated from 20px to 24px minimum width
    const comparisonColumnWidth = 10;
    const comparisonOffset = 12; // Gap between main and comparison columns
    
    // Use consistent 32px padding for dashboard cards to match Today vs Previous Years chart
    const columnPadding = 32; // Use 32px padding for consistent dashboard card styling
    const columnAreaStartX = gridStartX + columnPadding;
    const columnAreaWidth = gridWidth - (2 * columnPadding);
    
    // Calculate positioning based on comparison mode with left-middle-right distribution
    let columnGroupWidth, totalColumnsWidth, sidePadding, gapBetweenGroups;
    
    if (hasCompare) {
      // When comparison mode is enabled, treat main + comparison as a single "column group"
      columnGroupWidth = mainColumnWidth + comparisonOffset + comparisonColumnWidth;
      totalColumnsWidth = N * columnGroupWidth;
      const availableWidth = columnAreaWidth; // Use column area width (with padding)
      
      if (N === 1) {
        // Single column group: center it
        sidePadding = columnAreaStartX + (availableWidth - columnGroupWidth) / 2;
        gapBetweenGroups = 0;
      } else if (N === 2) {
        // Two column groups: left edge and right edge (within padded area)
        sidePadding = columnAreaStartX;
        gapBetweenGroups = availableWidth - totalColumnsWidth;
      } else {
        // Multiple column groups: first at left, last at right, others evenly distributed (within padded area)
        sidePadding = columnAreaStartX;
        gapBetweenGroups = (availableWidth - totalColumnsWidth) / (N - 1);
      }
    } else {
      // When only main columns are shown, use left-middle-right distribution
      columnGroupWidth = mainColumnWidth;
      totalColumnsWidth = N * mainColumnWidth;
      const availableWidth = columnAreaWidth; // Use column area width (with padding)
      
      if (N === 1) {
        // Single column: center it
        sidePadding = columnAreaStartX + (availableWidth - columnGroupWidth) / 2;
        gapBetweenGroups = 0;
      } else if (N === 2) {
        // Two columns: left edge and right edge (within padded area)
        sidePadding = columnAreaStartX;
        gapBetweenGroups = availableWidth - totalColumnsWidth;
      } else {
        // Multiple columns: first at left, last at right, others evenly distributed (within padded area)
        sidePadding = columnAreaStartX;
        gapBetweenGroups = (availableWidth - totalColumnsWidth) / (N - 1);
      }
    }

    // Create chart group
    const chartGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    chartGroup.setAttribute('transform', `translate(${padding.left}, ${padding.top})`);

    // Draw grid lines (inset by 24px from axis labels on both sides)
    this.drawGridLines(chartGroup, gridWidth, chartHeight, maxSales, gridStartX);

    // Draw Y-axis labels flush to SVG edges
    this.drawYAxisLabels(chartGroup, chartHeight, maxSales, 'left', -padding.left); // Sales - flush to left SVG edge
    this.drawYAxisLabels(chartGroup, chartHeight, maxRoyalties, 'right', chartWidth + padding.right); // Royalties - flush to right SVG edge

    // --- MAIN COLUMN SYSTEM (ALWAYS RENDERED) ---
    const mainRoyaltiesLinePoints = [];
    
    // Draw main columns and collect royalties points (without sales values yet)
    const actualColumnHeights = []; // Store actual rendered column heights for text positioning
    for (let i = 0; i < N; i++) {
      const dataPoint = this.data[i];

      // Calculate main column position using unified positioning
      const columnGroupStartX = sidePadding + (i * (columnGroupWidth + gapBetweenGroups));
      const mainColumnX = columnGroupStartX;

      // Draw main stacked column
      const columnResult = this.drawStackedColumn(chartGroup, dataPoint, mainColumnX, mainColumnWidth, chartHeight, salesScale, i, false);
      actualColumnHeights[i] = columnResult.actualHeight;

      // Collect main royalties point
      const royaltiesValue = dataPoint.royalties !== undefined ? dataPoint.royalties : 0;
      const royaltiesY = chartHeight - (royaltiesValue * royaltiesScale);
      mainRoyaltiesLinePoints.push({ x: mainColumnX + mainColumnWidth / 2, y: royaltiesY, index: i });
    }
    
    // Draw grouped labels for main columns
    const groups = this.groupColumnsByTimeframe();
    const positioningInfo = {
      sidePadding,
      columnGroupWidth,
      gapBetweenGroups,
      columnAreaStartX: null,
      columnWidth: null,
      gapBetweenColumns: null,
      startX: null
    };
    this.drawGroupLabels(chartGroup, groups, positioningInfo, chartHeight);
    
    // Draw main royalties line BEFORE sales values so sales values appear on top
    if (mainRoyaltiesLinePoints.length > 1) {
      this.drawRoyaltiesLine(chartGroup, mainRoyaltiesLinePoints, false);
    }
    
    // Draw sales values on top of royalties line
    for (let i = 0; i < N; i++) {
      const dataPoint = this.data[i];
      const columnGroupStartX = sidePadding + (i * (columnGroupWidth + gapBetweenGroups));
      const mainColumnX = columnGroupStartX;

      // Draw column labels (sales values above columns) - time labels now handled by grouped labels
      // Pass actual column height for accurate text positioning
      this.drawColumnLabels(chartGroup, dataPoint, mainColumnX, mainColumnWidth, chartHeight, salesScale, actualColumnHeights[i]);
    }
    
    // Add royalties dots on top of everything
    this.addRoyaltiesDots(chartGroup);
    
    // --- COMPARISON COLUMN SYSTEM (CONDITIONAL) ---
    if (hasCompare) {
      const comparisonRoyaltiesLinePoints = [];
      
      // Draw comparison columns and collect royalties points
      for (let i = 0; i < N; i++) {
        // Get comparison data or create placeholder
        const compData = (this.options.compareData && i < this.options.compareData.length) 
          ? this.options.compareData[i] 
          : { 
              sales: 0, 
              royalties: 0, 
              month: this.data[i].month || '',
              year: this.data[i].year || '',
              values: [0],
              labels: ['']
            };
        
        // Calculate comparison column position using unified positioning
        const columnGroupStartX = sidePadding + (i * (columnGroupWidth + gapBetweenGroups));
        const comparisonColumnX = columnGroupStartX + mainColumnWidth + comparisonOffset;
        
        // Only draw comparison column if it has actual sales data
        if (compData.sales > 0) {
          const comparisonColumnElement = this.drawComparisonColumn(chartGroup, compData, comparisonColumnX, comparisonColumnWidth, chartHeight, salesScale, i);
          this.drawComparisonColumnLabels(chartGroup, compData, comparisonColumnX, comparisonColumnWidth, chartHeight, salesScale);

          // Create unified hover area spanning main column, gap, and comparison column
          this.addUnifiedComparisonHoverArea(chartGroup, columnGroupStartX, mainColumnWidth + comparisonOffset + comparisonColumnWidth, chartHeight, this.data[i], compData, i);
        }
        
        // Always collect comparison royalties point (even for placeholders)
        const royaltiesValue = compData.royalties !== undefined ? compData.royalties : 0;
        const royaltiesY = chartHeight - (royaltiesValue * royaltiesScale);
        comparisonRoyaltiesLinePoints.push({ x: comparisonColumnX + comparisonColumnWidth / 2, y: royaltiesY, index: i });
      }
      
      // Draw comparison royalties line
      if (comparisonRoyaltiesLinePoints.length > 1) {
        this.drawRoyaltiesLine(chartGroup, comparisonRoyaltiesLinePoints, true);
      }
    }

    this.svg.appendChild(chartGroup);
  }
  
  /**
   * Render scrollable stacked column chart with royalties support
   */
  renderScrollableStackedColumn() {
    // Clear previous royalties points
    this.royaltiesPoints = [];

    if (!this.data || this.data.length === 0) {
      console.warn('SnapChart: No data provided for scrollable stacked column chart');
      return;
    }



    // Validate data structure to prevent runtime crashes
    const validatedData = this.data.filter(d => {
      if (!d) return false;
      // Ensure values array exists and is valid
      if (!d.values || !Array.isArray(d.values) || d.values.length === 0) {
        console.warn('SnapChart: Invalid data point - missing or empty values array', d);
        return false;
      }
      // Ensure all values are numbers
      if (!d.values.every(v => typeof v === 'number' && !isNaN(v))) {
        console.warn('SnapChart: Invalid data point - values must be numbers', d);
        return false;
      }
      return true;
    });

    if (validatedData.length === 0) {
      console.warn('SnapChart: No valid data points after validation');
      return;
    }

    // Use validated data for the rest of the function
    this.data = validatedData;

    // Set up scrollable chart structure
    this.setupScrollableChart();

    const width = this.getScrollableCanvasWidth();
    const height = this.options.height;
    // Increase padding to accommodate axis labels and sales values above columns
    const padding = { top: 60, right: 40, bottom: 40, left: 40 }; // Added right padding for royalties axis
    const chartWidth = width - padding.left - padding.right;
    const chartHeight = height - padding.top - padding.bottom;
    
    // Grid lines are inset by 24px from axis labels on both sides
    const gridInset = 48;
    const gridWidth = chartWidth - (2 * gridInset); // Both left and right inset
    const gridStartX = gridInset;

    // Add symmetric padding inside the grid for columns (32px left, 32px right)
    // Restore 32px padding for all charts including Today vs Previous Years
    const columnPaddingLeft = 32;
    const columnPaddingRight = 32;
    const columnAreaStartX = gridStartX + columnPaddingLeft;
    const columnAreaWidth = gridWidth - columnPaddingLeft - columnPaddingRight; // 32px left + 32px right padding inside grid

    // Column positioning for scrollable chart
    const N = this.data.length;

    // Calculate scales for dual axis - use the highest individual value
    let maxSales = Math.max(...this.data.map(d => {
      const salesValue = d.sales || 0;
      const valuesSum = d.values ? d.values.reduce((sum, v) => sum + (v || 0), 0) : 0;
      return Math.max(salesValue, valuesSum);
    }));
    let maxRoyalties = Math.max(...this.data.map(d => d.royalties || 0));

    // Use consistent scaling with stacked column chart (no padding) for proportional segment heights
    const salesScale = chartHeight / (maxSales || 1); // Prevent division by zero
    const royaltiesScale = chartHeight / (maxRoyalties || 1); // Prevent division by zero

    // Column dimensions - NEW LAYOUT STRATEGY for Today vs Previous Years chart
    let columnWidth, gapBetweenColumns, totalColumnsWidth;

    // NEW LAYOUT STRATEGY: Full-width distribution for Today vs Previous Years chart
    if (this.options.isTodayVsPreviousYearsChart) {
      // Fixed column width
      columnWidth = 32; // Fixed column width exactly 32px
      
      // Available space for columns (excluding 32px padding on each side)
      const availableWidth = columnAreaWidth;

      if (this.options.fullWidthDistribution && N > 1) {
        // Full-width distribution: spread columns across entire available width
        // Calculate gaps to distribute columns evenly
        const totalColumnSpace = N * columnWidth;
        const remainingSpace = availableWidth - totalColumnSpace;
        gapBetweenColumns = remainingSpace / (N - 1);
        
        // Ensure minimum gap of 32px
        if (gapBetweenColumns < 20) {
          gapBetweenColumns = 20;
        }
      } else {
        // Fallback to minimum gap approach
        const minGapWidth = 20; // Minimum gap of 32px between adjacent columns
        
        // Calculate total space needed for all columns
        const totalColumnSpace = N * columnWidth;
        const totalMinGapSpace = (N - 1) * minGapWidth;
        const totalRequiredSpace = totalColumnSpace + totalMinGapSpace;

        if (totalRequiredSpace <= availableWidth) {
          // We have enough space - distribute extra space as larger gaps
          const extraSpace = availableWidth - totalRequiredSpace;
          gapBetweenColumns = minGapWidth + (N > 1 ? extraSpace / (N - 1) : 0);
        } else {
          // Not enough space - use minimum gaps (chart will scroll)
          gapBetweenColumns = minGapWidth;
        }
      }

      // Total width is the actual space used by all columns and gaps
      totalColumnsWidth = N * columnWidth + (N - 1) * gapBetweenColumns;
    } else {
      // Default fixed width for other charts
      columnWidth = 24; // Updated from 20px to 24px minimum width
      gapBetweenColumns = 24; // Increased gap between columns (50% more than 16px)
      totalColumnsWidth = N * columnWidth + (N - 1) * gapBetweenColumns;
    }

    // Calculate if scrolling is needed based on column area width
    const needsScrolling = totalColumnsWidth > columnAreaWidth;
    
    // Set up the scrollable width - include symmetric column padding in calculations
    const scrollableWidth = totalColumnsWidth + columnPaddingLeft + columnPaddingRight; // Account for padding inside grid

    // === BACKGROUND SVG (Fixed Elements) ===
    this.backgroundSvg.setAttribute('viewBox', `0 0 ${width} ${height}`);
    
    const backgroundGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    backgroundGroup.setAttribute('transform', `translate(${padding.left}, ${padding.top})`);
    
    // Draw grid lines in background (fixed)
    this.drawGridLines(backgroundGroup, gridWidth, chartHeight, maxSales, gridStartX);
    
    // Draw Y-axis labels flush to SVG edges in background (fixed)
    // For Today vs Previous Years chart, use same edge positioning as standard charts
    if (this.options.isTodayVsPreviousYearsChart) {
      this.drawYAxisLabels(backgroundGroup, chartHeight, maxSales, 'left', -padding.left); // Sales - flush to left SVG edge
      this.drawYAxisLabels(backgroundGroup, chartHeight, maxRoyalties, 'right', chartWidth + padding.right); // Royalties - flush to right SVG edge
    } else {
      this.drawYAxisLabels(backgroundGroup, chartHeight, maxSales, 'left', -padding.left); // Sales - flush to left SVG edge
      this.drawYAxisLabels(backgroundGroup, chartHeight, maxRoyalties, 'right', chartWidth + padding.right); // Royalties - flush to right SVG edge
    }
    
    this.backgroundSvg.appendChild(backgroundGroup);

    // === CONTENT SVG (Scrollable Elements) ===
    // For Today vs Previous Years chart, implement right-anchored scrollable behavior
    if (this.options.isTodayVsPreviousYearsChart) {
      // Calculate total width needed for all columns within grid boundaries
      // Content should extend beyond container width but columns should stay within grid
      const totalContentWidth = totalColumnsWidth + padding.left + padding.right + (columnPaddingLeft + columnPaddingRight);

      // Set content SVG to the full width needed for all columns
      this.contentSvg.setAttribute('viewBox', `0 0 ${totalContentWidth} ${height}`);
      this.contentSvg.style.width = `${totalContentWidth}px`;
      this.contentSvg.style.minWidth = `${totalContentWidth}px`;
      this.contentSvg.style.height = '100%';

      // Store dimensions for scroll positioning with proper grid boundary constraints
      this.todayVsPreviousYearsScrollInfo = {
        totalContentWidth: totalContentWidth,
        containerWidth: width,
        needsScrolling: totalContentWidth > width,
        rightPadding: columnPaddingRight, // Use actual column padding for grid boundary
        leftPadding: columnPaddingLeft, // Store left padding for boundary calculations
        columnPaddingRight: columnPaddingRight, // Store for boundary calculations
        gridWidth: gridWidth, // Store grid width for boundary calculations
        gridStartX: gridStartX, // Store grid start position
        columnAreaStartX: columnAreaStartX, // Store column area start for positioning
        columnAreaWidth: columnAreaWidth, // Store column area width
        totalColumnsWidth: totalColumnsWidth // Store total columns width
      };
    } else {
      // Standard behavior for other chart types
      const extraRightPadding = gridInset + columnPaddingRight;
      const contentSvgWidth = scrollableWidth + padding.left + padding.right + extraRightPadding;
      this.contentSvg.setAttribute('viewBox', `0 0 ${contentSvgWidth} ${height}`);
      this.contentSvg.style.width = `${contentSvgWidth}px`;
      this.contentSvg.style.minWidth = `${contentSvgWidth}px`; // Force minimum width
    }
    
    const contentGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    contentGroup.setAttribute('transform', `translate(${padding.left}, ${padding.top})`);

    // Draw columns and data in content (scrollable)
    const royaltiesLinePoints = [];
    
    // Draw columns and collect royalties points (without sales values yet)
    const actualColumnHeights = []; // Store actual rendered column heights for text positioning
    for (let i = 0; i < N; i++) {
      const dataPoint = this.data[i];

      // Calculate column position using the pre-calculated dimensions
      const columnX = columnAreaStartX + (i * (columnWidth + gapBetweenColumns));

      // Draw stacked column
      const columnResult = this.drawStackedColumn(contentGroup, dataPoint, columnX, columnWidth, chartHeight, salesScale, i, false);
      actualColumnHeights[i] = columnResult.actualHeight;

      // Collect royalties point for line
      const royaltiesValue = dataPoint.royalties !== undefined ? dataPoint.royalties : 0;
      const royaltiesY = chartHeight - (royaltiesValue * royaltiesScale);
      royaltiesLinePoints.push({ x: columnX + columnWidth / 2, y: royaltiesY, index: i });
    }

    // Add hover events for scrollable stacked columns (needed for Today vs Previous Years chart)
    if (N > 0 && N <= 180) {
      // For scrollable stacked columns, add individual hover areas for each column
      for (let i = 0; i < N; i++) {
        const dataPoint = this.data[i];

        // Use the same positioning logic as column drawing
        const columnX = columnAreaStartX + (i * (columnWidth + gapBetweenColumns));

        // Create hover area for this column
        const hoverArea = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        hoverArea.classList.add('snap-chart-column-hover-area');
        hoverArea.setAttribute('x', columnX);
        hoverArea.setAttribute('y', 0);
        hoverArea.setAttribute('width', columnWidth);
        hoverArea.setAttribute('height', chartHeight);
        hoverArea.setAttribute('fill', 'transparent');
        hoverArea.setAttribute('pointer-events', 'all');

        // Add hover events
        hoverArea.addEventListener('mouseenter', (e) => {
          if (this.isSliderDragging) return;
          this.showTooltip(e, dataPoint, i);
          this.showRoyaltiesDot(i);
        });

        hoverArea.addEventListener('mouseleave', () => {
          this.hideTooltip();
          this.hideAllRoyaltiesDots();
        });

        contentGroup.appendChild(hoverArea);
      }
    }
    
    // Draw labels for columns
    if (this.options.isTodayVsPreviousYearsChart) {
      // Draw custom two-line labels for Today vs Previous Years chart
      this.drawTodayVsPreviousYearsLabels(contentGroup, columnAreaStartX, columnWidth, gapBetweenColumns, chartHeight);
    } else {
      // Draw grouped labels for other scrollable columns
      const groups = this.groupColumnsByTimeframe();

      const positioningInfo = {
        sidePadding: null,
        columnGroupWidth: null,
        gapBetweenGroups: null,
        columnAreaStartX,
        columnWidth,
        gapBetweenColumns,
        startX: null
      };
      this.drawGroupLabels(contentGroup, groups, positioningInfo, chartHeight);
    }
    
    // Draw royalties line BEFORE sales values so sales values appear on top
    if (royaltiesLinePoints.length > 1) {
      this.drawRoyaltiesLine(contentGroup, royaltiesLinePoints, false);
    }
    
    // Draw sales values on top of royalties line
    for (let i = 0; i < N; i++) {
      const dataPoint = this.data[i];
      const columnX = columnAreaStartX + (i * (columnWidth + gapBetweenColumns));

      // Draw column labels (sales values above columns) - time labels now handled by grouped labels
      // Pass actual column height for accurate text positioning
      this.drawColumnLabels(contentGroup, dataPoint, columnX, columnWidth, chartHeight, salesScale, actualColumnHeights[i]);
    }
    
    // Add royalties dots on top of everything
    this.addRoyaltiesDots(contentGroup);
    
    this.contentSvg.appendChild(contentGroup);
    
    // Add scrollable class to container for CSS styling
    this.containerElement.classList.add('snap-chart-scrollable');
    
    // Auto-scroll to the end (right side) to show latest data
    if (this.scrollableContainer) {
      // Use requestAnimationFrame to ensure DOM is fully updated before scrolling
      requestAnimationFrame(() => {
        if (this.options.isTodayVsPreviousYearsChart) {
          // For Today vs Previous Years: Right-anchored behavior
          this.setupTodayVsPreviousYearsScrolling();
        } else {
          // Standard behavior: Scroll to the far right to show the latest sales data
          this.scrollableContainer.scrollLeft = this.scrollableContainer.scrollWidth - this.scrollableContainer.clientWidth;

          // Update custom scrollbar after scrolling
          this.updateCustomScrollbar(this.scrollableContainer);
        }
      });
    }
  }
  
  /**
   * Render daily sales history chart with single columns and date range filtering
   */
  renderDailySalesHistory() {
    // Initialize with empty data if no data provided
    if (!this.data || this.data.length === 0) {
      console.warn('SnapChart: No data provided for daily sales history chart');
      this.data = [];
    } else {
      // Validate data structure to prevent runtime crashes
      const validatedData = this.data.filter(d => {
        if (!d) return false;
        // For daily sales history, we need sales value and date info
        if (typeof d.sales !== 'number' || isNaN(d.sales)) {
          console.warn('SnapChart: Invalid data point - sales must be a number', d);
          return false;
        }
        return true;
      });

      if (validatedData.length === 0) {
        console.warn('SnapChart: No valid data points after validation');
        this.data = [];
      } else {
        // Use validated data for the rest of the function
        this.data = validatedData;
      }
    }

    const width = this.getCanvasWidth();
    const height = this.options.height;
    // Padding to accommodate axis labels, date range controls, and returns segments
    // Increased bottom padding to prevent returns overlap with date labels and controls
    const padding = { top: 40, right: 40, bottom: 140, left: 40 }; // Increased from 100px to 140px
    const chartWidth = width - padding.left - padding.right;
    const chartHeight = height - padding.top - padding.bottom;
    
    // Grid lines are inset by 24px from axis labels on both sides
    const gridInset = 48;
    const gridWidth = chartWidth - (2 * gridInset);
    const gridStartX = gridInset;

    // Calculate column positioning for daily sales history
    const N = this.data.length;

    // Calculate scales for dual axis - handle empty data case and accommodate returns
    let maxSales = 0;
    let maxRoyalties = 0;
    let maxReturns = 0;
    
    if (N > 0) {
      maxSales = Math.max(...this.data.map(d => d.sales || 0));
      maxRoyalties = Math.max(...this.data.map(d => d.royalties || 0));
      maxReturns = Math.max(...this.data.map(d => d.returns || 0));
      
      // Add some padding to the top (10% more) for better visual spacing
      maxSales = Math.ceil(maxSales * 1.1);
      maxRoyalties = Math.ceil(maxRoyalties * 1.1);
      maxReturns = Math.ceil(maxReturns * 1.1);
    } else {
      // For empty data, set reasonable default scales
      maxSales = 100;
      maxRoyalties = 100;
      maxReturns = 10;
    }
    
    // Sales scale uses the full chart height (baseline is at bottom)
    const salesScale = chartHeight / (maxSales || 1);
    const royaltiesScale = chartHeight / (maxRoyalties || 1);
    const returnsScale = salesScale; // Use same scale as sales for consistency
    
    // Baseline is ALWAYS at the bottom of the chart area (bottom horizontal line)
    const baselineY = chartHeight;
    
    // Fixed 32px padding inside grid boundaries - no centering
    const FIXED_PADDING = 32; // Exactly 32px padding on each side inside grid
    const columnAreaStartX = gridStartX + FIXED_PADDING; // Exactly 32px from grid start
    const columnAreaEndX = gridStartX + gridWidth - FIXED_PADDING; // Exactly 32px from grid end
    const columnAreaWidth = columnAreaEndX - columnAreaStartX; // Available width for columns
    
    // Fixed positioning: first column at left edge, last column at right edge
    // Calculate column width that allows proper spacing
    const minColumnWidth = 1; // Minimum 1px column width
    let columnWidth = N > 0 ? Math.max(minColumnWidth, Math.floor(columnAreaWidth / (N * 1.5))) : minColumnWidth;
    
    let gapBetweenColumns;
    
    if (N === 0) {
      // No data: set defaults for empty chart
      columnWidth = minColumnWidth;
      gapBetweenColumns = 0;
    } else if (N === 1) {
      // Single column: position at left edge with reasonable width, not full width
      columnWidth = Math.min(columnAreaWidth, Math.max(24, columnAreaWidth / 4)); // Max 1/4 of available width or 24px minimum
      gapBetweenColumns = 0;
    } else if (N === 2) {
      // Two columns: one at left edge, one at right edge
      columnWidth = Math.max(minColumnWidth, Math.floor(columnAreaWidth / 10)); // Reasonable width
      gapBetweenColumns = columnAreaWidth - (2 * columnWidth); // Remaining space between them
    } else {
      // Multiple columns: evenly distributed from left edge to right edge
      // Calculate spacing so first column is at left, last at right, others evenly between
      const totalSpacingWidth = columnAreaWidth - (N * columnWidth);
      gapBetweenColumns = totalSpacingWidth / (N - 1); // Equal gaps between all columns
    }
    
    // Fixed positioning system - no centering
    const startX = columnAreaStartX; // First column starts at left edge (32px from grid start)

    // Create chart group
    const chartGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    chartGroup.setAttribute('transform', `translate(${padding.left}, ${padding.top})`);

    // Draw grid lines
    this.drawGridLines(chartGroup, gridWidth, chartHeight, maxSales, gridStartX);

    // Draw Y-axis labels flush to SVG edges
    this.drawYAxisLabels(chartGroup, chartHeight, maxSales, 'left', -padding.left); // Sales - flush to left SVG edge
    this.drawYAxisLabels(chartGroup, chartHeight, maxRoyalties, 'right', chartWidth + padding.right); // Royalties - flush to right SVG edge

    // Draw columns and collect royalties points
    const royaltiesLinePoints = [];
    
    for (let i = 0; i < N; i++) {
      const dataPoint = this.data[i];
      
      // Calculate column position
      const columnX = startX + (i * (columnWidth + gapBetweenColumns));
      
      // Draw single column with returns support
      const columnGroup = this.drawSingleColumn(chartGroup, dataPoint, columnX, columnWidth, chartHeight, salesScale, i, returnsScale, baselineY);
      
      // Collect royalties point for line (adjusted for new baseline)
      const royaltiesValue = dataPoint.royalties !== undefined ? dataPoint.royalties : 0;
      const royaltiesY = baselineY - (royaltiesValue * royaltiesScale);
      royaltiesLinePoints.push({ x: columnX + columnWidth / 2, y: royaltiesY, index: i });
    }
    
    // Always generate month boundaries and labels based on selected date range
    // Use current date range from options, fallback to reasonable defaults
    let labelStartDate = this.options.currentStartDate;
    let labelEndDate = this.options.currentEndDate;
    
    // If no current date range is set, use data range or minimal defaults
    if (!labelStartDate || !labelEndDate) {
      if (N > 0 && this.data[0].dateObj && this.data[this.data.length - 1].dateObj) {
        labelStartDate = this.data[0].dateObj;
        labelEndDate = this.data[this.data.length - 1].dateObj;
      } else {
        // Minimal default range if no data and no date range
        labelStartDate = new Date();
        labelEndDate = new Date();
      }
    }
    
    // Always set month boundaries for labeling based on the selected/determined date range
    this.monthBoundaries = this.getMonthBoundariesForLabeling(labelStartDate, labelEndDate);
    
    // Always draw month labels, regardless of whether there's sales data
    // Use grouped labels for daily sales history as well
    if (this.shouldShowDailyLabels()) {
      // For daily labels (30 columns or less), use existing daily label system
    this.drawMonthLabelsFromDateRange(chartGroup, startX, chartWidth, chartHeight, labelStartDate, labelEndDate);
    } else {
      // For month/quarter/year labels, use grouped system
      // Pass the actual calculated positioning values from daily sales history
      const groups = this.groupColumnsByTimeframe();
      const positioningInfo = {
        sidePadding: null,
        columnGroupWidth: null,
        gapBetweenGroups: null,
        columnAreaStartX: null,
        columnWidth,
        gapBetweenColumns,
        startX,
        // Add daily sales specific positioning info
        isDailySales: true,
        actualColumnPositions: this.calculateActualColumnPositions(startX, columnWidth, gapBetweenColumns, N),
        // Add date range info for time-based positioning
        dateRange: {
          startDate: labelStartDate,
          endDate: labelEndDate,
          chartWidth: columnAreaWidth,
          chartStartX: columnAreaStartX
        }
      };
      this.drawGroupLabels(chartGroup, groups, positioningInfo, chartHeight);
    }
    
    // Show hover events for up to 180 columns (these columns have enough width for accurate hover)
    if (N > 0 && N <= 180) {
      // For 180 or fewer columns: Use individual hover areas for better accuracy
      this.addIndividualDailySalesHoverEvents(chartGroup, startX, columnWidth, gapBetweenColumns, chartHeight);
    }
    
    // Royalties line removed for daily sales history - shown only in tooltips to avoid clutter with many columns
    
    // Add date range controls with 16px gap below month labels
    // Month labels are at chartHeight + 20, year labels at chartHeight + 35
    // So date range controls should be at chartHeight + 35 + 16 = chartHeight + 51
    // With increased bottom padding (140px), there's now more space for returns segments
    // Always create date range controls even if there's no data
    this.createDateRangeControls(chartGroup, chartWidth, chartHeight + 51);
    
    this.svg.appendChild(chartGroup);

    // Update insights with current data
    this.updateInsights();
    
    // Add daily sales history class to container for CSS styling
    this.containerElement.classList.add('snap-chart-daily-sales');
  }
  
  /**
   * Process pie chart data to ensure minimum slice visibility while preserving proportional relationships
   */
  processSliceAngles(data) {
    const gapAngle = 0; // No gaps between slices
    const availableAngle = 360 - (data.length * gapAngle); // Total angle minus gaps
    
    // Calculate initial angles based on percentages
    const initialAngles = data.map(item => ({
      ...item,
      originalAngle: (item.percentage / 100) * availableAngle
    }));
    
    // Graduated minimum system to preserve proportional relationships
    const processedAngles = initialAngles.map(item => {
      const percentage = item.percentage;
      let targetAngle = item.originalAngle;
      
      // Only apply minimum width to very small slices (< 0.8%)
      if (percentage < 0.8) {
        // Minimum 2 degrees for visibility, but preserve some proportional difference
        const minAngle = 2;
        const proportionalBoost = Math.max(minAngle, item.originalAngle * 3); // 3x boost for tiny slices
        targetAngle = Math.min(proportionalBoost, minAngle + (percentage * 2)); // Cap the boost
      }
      // For slices between 0.8% and 2%, apply a smaller proportional boost
      else if (percentage < 2) {
        targetAngle = item.originalAngle * 1.5; // 1.5x boost for small slices
      }
      // Slices >= 2% keep their original proportions
      
      return {
        ...item,
        targetAngle,
        needsAdjustment: targetAngle > item.originalAngle
      };
    });
    
    // Calculate total extra space needed
    const totalExtraNeeded = processedAngles.reduce((sum, item) => 
      sum + Math.max(0, item.targetAngle - item.originalAngle), 0);
    
    // Find slices that can give up space (> 5% of total)
    const slicesCanGiveSpace = processedAngles.filter(item => item.percentage > 5);
    const totalAvailableFromLarger = slicesCanGiveSpace.reduce((sum, item) => 
      sum + item.originalAngle, 0);
    
    if (totalExtraNeeded === 0) {
      // No adjustments needed
      return processedAngles.map(item => ({
        ...item,
        adjustedAngle: item.originalAngle
      }));
    }
    
    if (totalAvailableFromLarger <= totalExtraNeeded) {
      // Not enough space from larger slices, scale down all adjustments proportionally
      const scaleFactor = Math.min(1, totalAvailableFromLarger / totalExtraNeeded);
      return processedAngles.map(item => ({
        ...item,
        adjustedAngle: item.originalAngle + (item.targetAngle - item.originalAngle) * scaleFactor
      }));
    }
    
    // Calculate reduction factor for larger slices only
    const reductionFactor = totalExtraNeeded / totalAvailableFromLarger;
    
    // Apply adjustments
    return processedAngles.map(item => ({
      ...item,
      adjustedAngle: item.percentage > 5 
        ? item.originalAngle * (1 - reductionFactor) // Reduce large slices
        : item.targetAngle // Apply boost to small slices
    }));
  }

  /**
   * Render pie chart
   */
  renderPieChart() {
    if (!this.data || this.data.length === 0) {
      console.warn('SnapChart: No data available for pie chart');
      return;
    }
    
    if (!this.canvas) {
      console.error('SnapChart: Canvas not found for pie chart');
      return;
    }
    
    // Clear the canvas
    this.canvas.innerHTML = '';
    
    // Create pie chart container
    const pieContainer = document.createElement('div');
    pieContainer.className = 'snap-chart-pie';
    
    // Calculate total value and ensure percentages add up
    const totalValue = this.data.reduce((sum, item) => sum + (item.value || 0), 0);
    
    if (totalValue === 0) {
      console.warn('SnapChart: Total value is zero, cannot render pie chart');
      return;
    }
    
    // Recalculate percentages if not provided or if they don't add up to 100
    const processedData = this.data.map(item => ({
      ...item,
      percentage: totalValue > 0 ? (item.value / totalValue) * 100 : 0
    }));
    
    // Process slice angles to ensure minimum visibility
    const processedSlices = this.processSliceAngles(processedData);
    
    // Create SVG for pie chart - matching Figma dimensions
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svg.setAttribute('class', 'snap-chart-pie-svg');
    svg.setAttribute('viewBox', '0 0 170.59 170.59');
    svg.setAttribute('width', '170.59');
    svg.setAttribute('height', '170.59');
    
    // No SVG filter needed - removed to prevent blur
    
    // Pie chart dimensions - matching Figma
    const centerX = 85.295; // Half of 170.59
    const centerY = 85.295;
    const outerRadius = 85.295; // Full radius to edges
    const innerRadius = 63.9; // Increased slice width by 40% (was 70, now 63.9 for 40% thicker slices)
    
    // Sort data by value (largest first) for sequential animation
    const sortedData = processedSlices
      .map((dataPoint, originalIndex) => ({ ...dataPoint, originalIndex }))
      .filter(dataPoint => dataPoint.value > 0)
      .sort((a, b) => b.value - a.value);
    
    // Generate pie slices with no gaps
    let currentAngle = -90; // Start at top (-90 degrees)
    const gapAngle = 0; // No gaps between slices
    
    // Store slice information for animation
    const sliceInfo = [];
    
    processedSlices.forEach((dataPoint, index) => {
      if (dataPoint.value > 0) {
        // Use the adjusted angle from minimum width processing
        const sliceAngle = dataPoint.adjustedAngle;
        
        const startAngle = currentAngle + (gapAngle / 2);
        const endAngle = startAngle + sliceAngle;
        
        // Create pie slice path - initially with zero angle (for animation)
        const pathElement = this.createPieSlicePath(
          centerX, centerY, innerRadius, outerRadius, 
          startAngle, startAngle, index, dataPoint // Start with zero angle
        );
        
        // Store slice information for animation
        sliceInfo.push({
          pathElement,
          dataPoint,
          index,
          startAngle,
          endAngle,
          originalIndex: index
        });
        
        // Add event listeners for hover and click
        this.addPieSliceEvents(pathElement, dataPoint, index);
        
        svg.appendChild(pathElement);
        currentAngle = endAngle + (gapAngle / 2);
      }
    });
    
    // Create center text
    const centerText = this.createPieCenterText(centerX, centerY);
    svg.appendChild(centerText);
    
    // Add SVG to container
    pieContainer.appendChild(svg);
    
    // Modify canvas height for pie chart - matching Figma size
    this.canvas.style.height = '200px'; // Reduced to accommodate smaller pie chart
    this.canvas.style.display = 'flex';
    this.canvas.style.alignItems = 'center';
    this.canvas.style.justifyContent = 'center';
    
    // Add container to canvas
    this.canvas.appendChild(pieContainer);
    
    // Add inner and outer borders if enabled (for sold colors pie chart)
    if (this.options.showSliceBorders) {
      this.addPieChartBorders(svg, centerX, centerY, innerRadius, outerRadius);
    }
    
    // Create pie chart tooltip
    this.createPieTooltip();
    
    // Add pie chart class to container for CSS styling
    this.containerElement.classList.add('snap-chart-pie-type');
    
    // Start sequential animation
    this.startPieChartSequentialAnimation(sliceInfo, centerText, centerX, centerY, innerRadius, outerRadius, gapAngle);
  }
  
  /**
   * Add inner and outer borders to pie chart (for sold colors visibility)
   */
  addPieChartBorders(svg, centerX, centerY, innerRadius, outerRadius) {
    const strokeWidth = 0.5; // 50% less than original 1px
    
    // Outer border circle - adjust radius inward to prevent clipping
    const outerBorder = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
    outerBorder.setAttribute('cx', centerX);
    outerBorder.setAttribute('cy', centerY);
    outerBorder.setAttribute('r', outerRadius - (strokeWidth / 2)); // Move stroke inside
    outerBorder.setAttribute('fill', 'none');
    outerBorder.setAttribute('stroke', '#E9EBF2');
    outerBorder.setAttribute('stroke-width', strokeWidth);
    outerBorder.setAttribute('class', 'snap-chart-pie-border-outer');
    
    // Inner border circle - adjust radius outward to prevent clipping
    const innerBorder = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
    innerBorder.setAttribute('cx', centerX);
    innerBorder.setAttribute('cy', centerY);
    innerBorder.setAttribute('r', innerRadius + (strokeWidth / 2)); // Move stroke inside
    innerBorder.setAttribute('fill', 'none');
    innerBorder.setAttribute('stroke', '#E9EBF2');
    innerBorder.setAttribute('stroke-width', strokeWidth);
    innerBorder.setAttribute('class', 'snap-chart-pie-border-inner');
    
    // Add borders to SVG (they should appear above the slices)
    svg.appendChild(outerBorder);
    svg.appendChild(innerBorder);
  }

  /**
   * Start sequential pie chart animation - largest slice first, smooth 1-second total duration
   */
  startPieChartSequentialAnimation(sliceInfo, centerText, centerX, centerY, innerRadius, outerRadius, gapAngle) {
    if (!sliceInfo || sliceInfo.length === 0) {
      // Show center text immediately if no slices
      if (centerText) {
        centerText.classList.add('visible');
      }
      return;
    }
    
    // Sort slices by value (largest first) for animation order
    const sortedSlices = sliceInfo
      .filter(slice => slice.dataPoint.value > 0)
      .sort((a, b) => b.dataPoint.value - a.dataPoint.value);
    
    // Animation parameters
    const totalDuration = 1000; // 1 second total
    const sliceDuration = totalDuration / sortedSlices.length; // Equal time per slice
    const easeOutQuart = t => 1 - Math.pow(1 - t, 4); // Smooth easing function
    
    let currentSliceIndex = 0;
    
    const animateNextSlice = () => {
      if (currentSliceIndex >= sortedSlices.length) {
        // Animation complete - show center text
        if (centerText) {
          setTimeout(() => {
            centerText.classList.add('visible');
          }, 100); // Small delay before showing center text
        }
        return;
      }
      
      const slice = sortedSlices[currentSliceIndex];
      const startTime = performance.now();
      
      const animateSlice = (currentTime) => {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / sliceDuration, 1);
        const easedProgress = easeOutQuart(progress);
        
        // Calculate current end angle based on progress
        const currentEndAngle = slice.startAngle + (slice.endAngle - slice.startAngle) * easedProgress;
        
        // Update the slice path
        const pathData = this.createPieSlicePathData(
          centerX, centerY, innerRadius, outerRadius, 
          slice.startAngle, currentEndAngle
        );
        
        slice.pathElement.setAttribute('d', pathData);
        
        if (progress < 1) {
          requestAnimationFrame(animateSlice);
        } else {
          // This slice is complete, start the next one
          currentSliceIndex++;
          animateNextSlice();
        }
      };
      
      requestAnimationFrame(animateSlice);
    };
    
    // Start the animation sequence
    animateNextSlice();
  }
  
  /**
   * Create SVG path data for pie slice (separated for animation)
   */
  createPieSlicePathData(centerX, centerY, innerRadius, outerRadius, startAngle, endAngle) {
    // Handle zero-angle case
    if (Math.abs(endAngle - startAngle) < 0.01) {
      return `M ${centerX} ${centerY} L ${centerX} ${centerY}`;
    }
    
    const startAngleRad = (startAngle * Math.PI) / 180;
    const endAngleRad = (endAngle * Math.PI) / 180;
    
    // Calculate arc points
    const x1 = centerX + outerRadius * Math.cos(startAngleRad);
    const y1 = centerY + outerRadius * Math.sin(startAngleRad);
    const x2 = centerX + outerRadius * Math.cos(endAngleRad);
    const y2 = centerY + outerRadius * Math.sin(endAngleRad);
    
    const x3 = centerX + innerRadius * Math.cos(endAngleRad);
    const y3 = centerY + innerRadius * Math.sin(endAngleRad);
    const x4 = centerX + innerRadius * Math.cos(startAngleRad);
    const y4 = centerY + innerRadius * Math.sin(startAngleRad);
    
    // Large arc flag
    const largeArcFlag = endAngle - startAngle > 180 ? 1 : 0;
    
    // Create path data
    return [
      `M ${x1} ${y1}`, // Move to start of outer arc
      `A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2}`, // Outer arc
      `L ${x3} ${y3}`, // Line to start of inner arc
      `A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${x4} ${y4}`, // Inner arc (reverse)
      'Z' // Close path
    ].join(' ');
  }
  
  /**
   * Create SVG path for pie slice with rounded corners to match Figma design
   */
  createPieSlicePath(centerX, centerY, innerRadius, outerRadius, startAngle, endAngle, index, dataPoint) {
    const startAngleRad = (startAngle * Math.PI) / 180;
    const endAngleRad = (endAngle * Math.PI) / 180;
    
    // Calculate arc points
    const x1 = centerX + outerRadius * Math.cos(startAngleRad);
    const y1 = centerY + outerRadius * Math.sin(startAngleRad);
    const x2 = centerX + outerRadius * Math.cos(endAngleRad);
    const y2 = centerY + outerRadius * Math.sin(endAngleRad);
    
    const x3 = centerX + innerRadius * Math.cos(endAngleRad);
    const y3 = centerY + innerRadius * Math.sin(endAngleRad);
    const x4 = centerX + innerRadius * Math.cos(startAngleRad);
    const y4 = centerY + innerRadius * Math.sin(startAngleRad);
    
    // Large arc flag
    const largeArcFlag = endAngle - startAngle > 180 ? 1 : 0;
    
    // Create path data - simplified approach for better rounded corner rendering
    const pathData = [
      `M ${x1} ${y1}`, // Move to start of outer arc
      `A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2}`, // Outer arc
      `L ${x3} ${y3}`, // Line to start of inner arc
      `A ${innerRadius} ${innerRadius} 0 ${largeArcFlag} 0 ${x4} ${y4}`, // Inner arc (reverse)
      'Z' // Close path
    ].join(' ');
    
    // Create path element
    const pathElement = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    pathElement.setAttribute('d', pathData);
    
    // For marketplace pie charts, use only the first 7 colors (base colors)
    const isMarketplacePie = dataPoint && this.marketplaceMapping[dataPoint.label];
    const colorIndex = isMarketplacePie ? (index % 7) : (index % 35);
    pathElement.setAttribute('class', `snap-chart-pie-slice snap-chart-pie-slice-${colorIndex}`);
    pathElement.setAttribute('data-index', index);
    
    // Support custom colors per slice (for color charts)
    if (dataPoint && dataPoint.color) {
      pathElement.style.fill = dataPoint.color;
    }
    
    // Add animation class
    pathElement.classList.add('snap-chart-pie-slice-animate');
    
    return pathElement;
  }
  
  /**
   * Create center text for pie chart
   */
  createPieCenterText(centerX, centerY) {
    const centerGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    centerGroup.setAttribute('class', 'snap-chart-pie-center-animate');
    
    // Use custom center label from options or default to 'Fit Type'
    const centerLabel = this.options.centerLabel || 'Fit Type';
    
    // Check if center label contains line break character
    if (centerLabel.includes('\n')) {
      // Multi-line text
      const lines = centerLabel.split('\n');
      const lineHeight = 16; // Line height in pixels
      const totalHeight = lines.length * lineHeight;
      const startY = centerY - (totalHeight / 2) + (lineHeight / 2);
      
      lines.forEach((line, index) => {
        const lineText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        lineText.setAttribute('x', centerX);
        lineText.setAttribute('y', startY + (index * lineHeight));
        lineText.setAttribute('text-anchor', 'middle');
        lineText.setAttribute('dominant-baseline', 'middle');
        lineText.setAttribute('class', 'snap-chart-pie-center-label');
        lineText.textContent = line;
        centerGroup.appendChild(lineText);
      });
    } else {
      // Single line text
      const labelText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      labelText.setAttribute('x', centerX);
      labelText.setAttribute('y', centerY);
      labelText.setAttribute('text-anchor', 'middle');
      labelText.setAttribute('dominant-baseline', 'middle');
      labelText.setAttribute('class', 'snap-chart-pie-center-label');
      labelText.textContent = centerLabel;
      centerGroup.appendChild(labelText);
    }
    
    return centerGroup;
  }
  
  /**
   * Add event listeners to pie slice
   */
  addPieSliceEvents(pathElement, dataPoint, index) {
    let isHovered = false;
    
    pathElement.addEventListener('mouseenter', (event) => {
      isHovered = true;
      this.showPieTooltip(event, dataPoint, index);
    });
    
    pathElement.addEventListener('mousemove', (event) => {
      if (isHovered) {
        this.updatePieTooltipPosition(event);
      }
    });
    
    pathElement.addEventListener('mouseleave', () => {
      isHovered = false;
      this.hidePieTooltip();
    });
    
    pathElement.addEventListener('click', () => {
      this.selectPieSlice(index);
    });
  }
  
  /**
   * Create pie chart tooltip
   */
  createPieTooltip() {
    if (this.pieTooltip) {
      this.pieTooltip.remove();
    }
    
    this.pieTooltip = document.createElement('div');
    this.pieTooltip.className = 'snap-chart-pie-tooltip';
    // Append to document body to avoid stacking context issues
    document.body.appendChild(this.pieTooltip);
  }
  
  /**
   * Show pie chart tooltip
   */
  showPieTooltip(event, dataPoint, index) {
    if (!this.pieTooltip) return;
    
    const label = dataPoint.label || 'Unknown';
    const value = dataPoint.value || 0;
    const percentage = dataPoint.percentage || 0;
    
    // Check if this is a marketplace pie chart by checking if label matches marketplace mapping
    const isMarketplacePie = this.marketplaceMapping[label];
    // Check if this is a sold colors pie chart by checking if dataPoint has a color property
    const isSoldColorsPie = dataPoint.color;
    
    if (isMarketplacePie) {
      // Marketplace pie chart with flag
      this.pieTooltip.innerHTML = `
        <div class="snap-chart-pie-tooltip-label">
          ${this.getFlagIconHtml(label)}
          <span style="margin-left: 6px;">${label}</span>
        </div>
        <div class="snap-chart-pie-tooltip-value-row">
          <span class="snap-chart-pie-tooltip-value-left">${value.toLocaleString()}</span>
          <span class="snap-chart-pie-tooltip-percentage-right">${percentage.toFixed(1)}%</span>
        </div>
      `;
      
      // Load flag after tooltip is shown
      this.loadTooltipFlags(this.pieTooltip);
    } else if (isSoldColorsPie) {
      // Sold colors pie chart with color circle
      this.pieTooltip.innerHTML = `
        <div class="snap-chart-pie-tooltip-label">
          <div class="snap-chart-pie-tooltip-color-circle" style="background-color: ${dataPoint.color};"></div>
          <span>${label}</span>
        </div>
        <div class="snap-chart-pie-tooltip-value-row">
          <span class="snap-chart-pie-tooltip-value-left">${value.toLocaleString()}</span>
          <span class="snap-chart-pie-tooltip-percentage-right">${percentage.toFixed(1)}%</span>
        </div>
      `;
    } else {
      // Regular pie chart without flag or color
      this.pieTooltip.innerHTML = `
        <div class="snap-chart-pie-tooltip-label">${label}</div>
        <div class="snap-chart-pie-tooltip-value-row">
          <span class="snap-chart-pie-tooltip-value-left">${value.toLocaleString()}</span>
          <span class="snap-chart-pie-tooltip-percentage-right">${percentage.toFixed(1)}%</span>
        </div>
      `;
    }
    
    this.updatePieTooltipPosition(event);
    this.pieTooltip.classList.add('visible');
  }
  
  /**
   * Update pie tooltip position
   */
  updatePieTooltipPosition(event) {
    if (!this.pieTooltip) return;
    
    // Since tooltip is now appended to document.body, use global coordinates
    const x = event.clientX;
    const y = event.clientY;
    
    // Position tooltip with offset and ensure it stays within viewport
    const tooltipRect = this.pieTooltip.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    let tooltipX = x + 10; // 10px offset from cursor
    let tooltipY = y - 10; // 10px above cursor
    
    // Prevent tooltip from going off screen
    if (tooltipX + tooltipRect.width > viewportWidth) {
      tooltipX = x - tooltipRect.width - 10; // Show to the left instead
    }
    if (tooltipY < 0) {
      tooltipY = y + 10; // Show below cursor instead
    }
    if (tooltipY + tooltipRect.height > viewportHeight) {
      tooltipY = viewportHeight - tooltipRect.height - 10;
    }
    
    this.pieTooltip.style.left = `${tooltipX}px`;
    this.pieTooltip.style.top = `${tooltipY}px`;
  }
  
  /**
   * Hide pie chart tooltip
   */
  hidePieTooltip() {
    if (this.pieTooltip) {
      this.pieTooltip.classList.remove('visible');
    }
  }
  
  /**
   * Select pie slice
   */
  selectPieSlice(index) {
    // Remove previous selection
    const previousSelected = this.containerElement.querySelector('.snap-chart-pie-slice.selected');
    if (previousSelected) {
      previousSelected.classList.remove('selected');
    }
    
    // Add selection to current slice
    const selectedSlice = this.containerElement.querySelector(`[data-index="${index}"]`);
    if (selectedSlice) {
      selectedSlice.classList.add('selected');
    }
    
    // Trigger selection event
    if (this.onSliceSelect) {
      this.onSliceSelect(this.data[index], index);
    }
  }
  
  /**
   * Set up scrollable chart structure - UPDATED for layered approach
   */
  setupScrollableChart() {
    console.log('Setting up scrollable chart');
    
    // Find the canvas element
    const canvas = this.containerElement.querySelector('.snap-chart-canvas');
    if (!canvas) {
      console.error('Canvas element not found');
      return;
    }
    
    // Clean up existing scrollbar before creating new one
    this.cleanupScrollbar();
    
    // Clear existing content
    canvas.innerHTML = '';
    
    // Create background SVG for fixed elements (grid + Y-axis)
    this.backgroundSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    this.backgroundSvg.classList.add('snap-chart-background-svg');
    this.backgroundSvg.setAttribute('preserveAspectRatio', 'xMidYMid meet');
    this.backgroundSvg.setAttribute('role', 'graphics-document');
    this.backgroundSvg.setAttribute('aria-label', 'Chart background with grid and axis labels');
    canvas.appendChild(this.backgroundSvg);
    
    // Create scrollable container
    const scrollableContainer = document.createElement('div');
    scrollableContainer.className = 'snap-chart-scrollable-container';
    
    // Create content SVG for scrollable elements (columns + data)
    this.contentSvg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    this.contentSvg.classList.add('snap-chart-content-svg');
    this.contentSvg.setAttribute('preserveAspectRatio', 'xMidYMid meet');
    this.contentSvg.setAttribute('role', 'graphics-document');
    this.contentSvg.setAttribute('aria-label', 'Scrollable chart content with columns and data');
    
    scrollableContainer.appendChild(this.contentSvg);
    canvas.appendChild(scrollableContainer);
    
    // Create chart content wrapper to ensure proper spacing
    const chartContentWrapper = document.createElement('div');
    chartContentWrapper.style.position = 'relative';
    chartContentWrapper.style.width = '100%';
    chartContentWrapper.style.height = '100%';
    
    // Move scrollable container into wrapper if needed
    // (No need to move as we're appending the scrollbar directly to canvas)
    
    // Create custom scrollbar - add directly to the canvas (not container) to avoid multiple scrollbars
    this.createCustomScrollbar(canvas, scrollableContainer);
    
    // Store references
    this.scrollableContainer = scrollableContainer;
    
    console.log('Scrollable chart setup complete', {
      canvas,
      scrollableContainer,
      backgroundSvg: this.backgroundSvg,
      contentSvg: this.contentSvg,
      customScrollbar: this.customScrollbar
    });
  }
  


  /**
   * Clean up existing scrollbar elements and event listeners
   */
  cleanupScrollbar() {
    console.log('Cleaning up existing scrollbar');
    
    // Remove existing scrollbar element from DOM
    if (this.customScrollbar && this.customScrollbar.parentNode) {
      this.customScrollbar.parentNode.removeChild(this.customScrollbar);
    }
    
    // Clean up event listeners
    if (this.customScrollbarHandlers) {
      if (this.scrollableContainer) {
        this.scrollableContainer.removeEventListener('scroll', this.customScrollbarHandlers.updateScrollbar);
      }
      if (this.scrollbarThumb) {
        this.scrollbarThumb.removeEventListener('mousedown', this.customScrollbarHandlers.handleThumbMouseDown);
      }
      if (this.scrollbarTrack) {
        this.scrollbarTrack.removeEventListener('click', this.customScrollbarHandlers.handleTrackClick);
      }
      document.removeEventListener('mousemove', this.customScrollbarHandlers.handleMouseMove);
      document.removeEventListener('mouseup', this.customScrollbarHandlers.handleMouseUp);
      this.customScrollbarHandlers = null;
    }
    
    // Clean up tooltip event listeners
    if (this.scrollbarTooltipHandlers) {
      if (this.customScrollbar) {
        this.customScrollbar.removeEventListener('mouseenter', this.scrollbarTooltipHandlers.showTooltip);
        this.customScrollbar.removeEventListener('mouseleave', this.scrollbarTooltipHandlers.hideTooltip);
      }
      if (this.scrollableContainer) {
        this.scrollableContainer.removeEventListener('scroll', this.scrollbarTooltipHandlers.hideTooltipOnScroll);
      }
      if (this.scrollbarThumb) {
        this.scrollbarThumb.removeEventListener('mousedown', this.scrollbarTooltipHandlers.onDragStart);
      }
      document.removeEventListener('mouseup', this.scrollbarTooltipHandlers.onDragEnd);
      this.scrollbarTooltipHandlers = null;
    }
    
    // Reset references
    this.customScrollbar = null;
    this.scrollbarThumb = null;
    this.scrollbarTrack = null;
    this.scrollbarTooltip = null;
    
    console.log('Scrollbar cleanup complete');
  }

  /**
   * Create custom scrollbar for scrollable chart
   */
  createCustomScrollbar(canvas, scrollableContainer) {
    console.log('Creating custom scrollbar');
    
    // Create scrollbar container
    const customScrollbar = document.createElement('div');
    customScrollbar.className = 'snap-chart-custom-scrollbar';
    
    // Create scrollbar track
    const scrollbarTrack = document.createElement('div');
    scrollbarTrack.className = 'snap-chart-scrollbar-track';
    
    // Create scrollbar thumb
    const scrollbarThumb = document.createElement('div');
    scrollbarThumb.className = 'snap-chart-scrollbar-thumb';
    
    // Create scrollbar tooltip as sibling to avoid opacity inheritance
    const scrollbarTooltip = document.createElement('div');
    scrollbarTooltip.className = 'snap-chart-scrollbar-tooltip';
    
    // Create tooltip content with icons
    scrollbarTooltip.innerHTML = `
      <svg class="snap-chart-scrollbar-tooltip-icon" viewBox="0 0 24 24">
        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
      </svg>
      <span>Scroll horizontally to show more data</span>
      <svg class="snap-chart-scrollbar-tooltip-icon" viewBox="0 0 24 24">
        <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
      </svg>
    `;
    
    // Append elements to avoid opacity inheritance
    scrollbarTrack.appendChild(scrollbarThumb);
    customScrollbar.appendChild(scrollbarTrack);
    canvas.appendChild(customScrollbar);
    // Append tooltip to canvas (not thumb) to avoid opacity inheritance
    canvas.appendChild(scrollbarTooltip);
    
    // Store references
    this.customScrollbar = customScrollbar;
    this.scrollbarThumb = scrollbarThumb;
    this.scrollbarTrack = scrollbarTrack;
    this.scrollbarTooltip = scrollbarTooltip;
    
    // Set up scrollbar functionality
    this.setupCustomScrollbarEvents(scrollableContainer, customScrollbar, scrollbarThumb, scrollbarTrack);
    
    // Set up tooltip functionality
    this.setupScrollbarTooltipEvents(scrollableContainer, customScrollbar, scrollbarThumb, scrollbarTooltip);
    
    // Initial update of scrollbar
    this.updateCustomScrollbar(scrollableContainer);
    
    console.log('Custom scrollbar created:', {
      customScrollbar,
      scrollbarTrack,
      scrollbarThumb,
      scrollbarTooltip,
      canvas,
      canvasRect: canvas.getBoundingClientRect()
    });
  }

  /**
   * Set up scrollbar tooltip event handlers - Fade away on scroll, show only on hover (not while dragging)
   */
  setupScrollbarTooltipEvents(scrollableContainer, customScrollbar, scrollbarThumb, scrollbarTooltip) {
    // Track dragging state to prevent tooltip during drag
    let isDragging = false;
    
    // Show tooltip on hover (only if not dragging)
    const showTooltip = () => {
      if (!isDragging) {
        this.positionScrollbarTooltip();
        scrollbarTooltip.classList.add('visible');
      }
    };
    
    // Hide tooltip
    const hideTooltip = () => {
      scrollbarTooltip.classList.remove('visible');
    };
    
    // Hide tooltip when scrolling starts (fade away immediately)
    const hideTooltipOnScroll = () => {
      scrollbarTooltip.classList.remove('visible');
    };
    
    // Track drag start - hide tooltip and set dragging state
    const onDragStart = () => {
      isDragging = true;
      scrollbarTooltip.classList.remove('visible');
    };
    
    // Track drag end - reset dragging state
    const onDragEnd = () => {
      isDragging = false;
    };
    
    // Add event listeners
    customScrollbar.addEventListener('mouseenter', showTooltip);
    customScrollbar.addEventListener('mouseleave', hideTooltip);
    scrollableContainer.addEventListener('scroll', hideTooltipOnScroll);
    scrollbarThumb.addEventListener('mousedown', onDragStart);
    document.addEventListener('mouseup', onDragEnd);
    
    // Store references for cleanup
    this.scrollbarTooltipHandlers = {
      showTooltip,
      hideTooltip,
      hideTooltipOnScroll,
      onDragStart,
      onDragEnd
    };
  }



  /**
   * Position scrollbar tooltip relative to thumb to avoid opacity inheritance
   */
  positionScrollbarTooltip() {
    if (!this.scrollbarTooltip || !this.scrollbarThumb || !this.customScrollbar) {
      return;
    }
    
    // Get the canvas element (where tooltip is appended)
    const canvas = this.containerElement.querySelector('.snap-chart-canvas');
    if (!canvas) return;
    
    // Get positions relative to the canvas
    const thumbRect = this.scrollbarThumb.getBoundingClientRect();
    const canvasRect = canvas.getBoundingClientRect();
    
    // Calculate thumb center relative to canvas
    const thumbCenterX = thumbRect.left + thumbRect.width / 2 - canvasRect.left;
    const thumbTopY = thumbRect.top - canvasRect.top;
    
    // Position tooltip centered above thumb with 8px gap
    const tooltipRect = this.scrollbarTooltip.getBoundingClientRect();
    const tooltipX = thumbCenterX - tooltipRect.width / 2;
    const tooltipY = thumbTopY - tooltipRect.height - 8;
    
    this.scrollbarTooltip.style.left = `${tooltipX}px`;
    this.scrollbarTooltip.style.top = `${tooltipY}px`;
  }

  /**
   * Set up custom scrollbar event handlers
   */
  setupCustomScrollbarEvents(scrollableContainer, customScrollbar, scrollbarThumb, scrollbarTrack) {
    let isDragging = false;
    let startX = 0;
    let startScrollLeft = 0;
    
    // Update scrollbar when container scrolls
    const updateScrollbar = () => {
      this.updateCustomScrollbar(scrollableContainer);
    };
    
    scrollableContainer.addEventListener('scroll', updateScrollbar);
    
    // Handle thumb drag
    const handleThumbMouseDown = (e) => {
      isDragging = true;
      startX = e.clientX;
      startScrollLeft = scrollableContainer.scrollLeft;
      scrollbarThumb.classList.add('dragging');
      
      // Prevent text selection during drag
      e.preventDefault();
      document.body.style.userSelect = 'none';
    };
    
    const handleMouseMove = (e) => {
      if (!isDragging) return;
      
      const deltaX = e.clientX - startX;
      const trackWidth = scrollbarTrack.offsetWidth;
      const thumbWidth = scrollbarThumb.offsetWidth;
      const maxThumbPosition = trackWidth - thumbWidth;
      
      // Calculate scroll position based on thumb movement
      const scrollRatio = deltaX / maxThumbPosition;
      const maxScrollLeft = scrollableContainer.scrollWidth - scrollableContainer.clientWidth;
      const newScrollLeft = Math.max(0, Math.min(maxScrollLeft, startScrollLeft + (scrollRatio * maxScrollLeft)));
      
      scrollableContainer.scrollLeft = newScrollLeft;
    };
    
    const handleMouseUp = () => {
      if (isDragging) {
        isDragging = false;
        scrollbarThumb.classList.remove('dragging');
        document.body.style.userSelect = '';
      }
    };
    
    // Handle track click (jump to position)
    const handleTrackClick = (e) => {
      if (e.target === scrollbarThumb) return; // Don't handle if clicking on thumb
      
      const rect = scrollbarTrack.getBoundingClientRect();
      const clickX = e.clientX - rect.left;
      const trackWidth = scrollbarTrack.offsetWidth;
      const thumbWidth = scrollbarThumb.offsetWidth;
      
      // Calculate target position (center thumb on click point)
      const targetThumbPosition = Math.max(0, Math.min(trackWidth - thumbWidth, clickX - thumbWidth / 2));
      const scrollRatio = targetThumbPosition / (trackWidth - thumbWidth);
      const maxScrollLeft = scrollableContainer.scrollWidth - scrollableContainer.clientWidth;
      
      scrollableContainer.scrollLeft = scrollRatio * maxScrollLeft;
    };
    
    // Add event listeners
    scrollbarThumb.addEventListener('mousedown', handleThumbMouseDown);
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    scrollbarTrack.addEventListener('click', handleTrackClick);
    
    // Store references for cleanup
    this.customScrollbarHandlers = {
      updateScrollbar,
      handleThumbMouseDown,
      handleMouseMove,
      handleMouseUp,
      handleTrackClick
    };
  }

  /**
   * Update custom scrollbar position and size
   */
  updateCustomScrollbar(scrollableContainer) {
    console.log('Updating custom scrollbar');
    
    if (!this.scrollbarThumb || !this.scrollbarTrack || !this.customScrollbar) {
      console.warn('Missing scrollbar elements:', {
        thumb: this.scrollbarThumb,
        track: this.scrollbarTrack,
        container: this.customScrollbar
      });
      return;
    }
    
    const containerWidth = scrollableContainer.clientWidth;
    const contentWidth = scrollableContainer.scrollWidth;
    const scrollLeft = scrollableContainer.scrollLeft;
    
    console.log('Scrollbar metrics:', {
      containerWidth,
      contentWidth,
      scrollLeft,
      needsScrolling: contentWidth > containerWidth
    });
    
    // Hide scrollbar if no scrolling is needed
    if (contentWidth <= containerWidth) {
      this.customScrollbar.classList.add('hidden');
      console.log('Scrollbar hidden - content fits in container');
      return;
    } else {
      this.customScrollbar.classList.remove('hidden');
      console.log('Scrollbar visible - content requires scrolling');
    }
    
    // Calculate thumb size as a proportion of visible content
    const trackWidth = this.scrollbarTrack.offsetWidth;
    const thumbWidth = Math.max(20, (containerWidth / contentWidth) * trackWidth); // Minimum 20px width
    
    // Calculate thumb position
    const maxScrollLeft = contentWidth - containerWidth;
    const scrollRatio = scrollLeft / maxScrollLeft;
    const maxThumbPosition = trackWidth - thumbWidth;
    const thumbPosition = scrollRatio * maxThumbPosition;
    
    console.log('Thumb metrics:', {
      trackWidth,
      thumbWidth,
      maxScrollLeft,
      scrollRatio,
      maxThumbPosition,
      thumbPosition
    });
    
    // Apply styles
    this.scrollbarThumb.style.width = `${thumbWidth}px`;
    this.scrollbarThumb.style.left = `${thumbPosition}px`;
    
    // Force scrollbar to be visible
    this.customScrollbar.style.display = 'block';
  }

  /**
   * Setup scrolling behavior for Today vs Previous Years chart with 32px right padding constraint
   */
  setupTodayVsPreviousYearsScrolling() {
    if (!this.scrollableContainer || !this.todayVsPreviousYearsScrollInfo) {
      return;
    }

    const { totalContentWidth, containerWidth, needsScrolling, rightPadding } = this.todayVsPreviousYearsScrollInfo;

    if (!needsScrolling) {
      // Content fits within container, no scrolling needed
      return;
    }

    // Enable horizontal scrolling for Today vs Previous Years chart
    this.scrollableContainer.style.overflowX = 'auto';
    this.scrollableContainer.style.overflowY = 'hidden';

    // Hide native scrollbar - use custom scrollbar only
    this.scrollableContainer.style.scrollbarWidth = 'none'; // Firefox
    this.scrollableContainer.style.msOverflowStyle = 'none'; // Internet Explorer 10+

    // Calculate maximum scroll position with 32px right padding constraint
    const maxScrollLeft = this.calculateMaxScrollWithRightPadding();

    // Set initial scroll position to show rightmost columns with 32px padding
    this.scrollableContainer.scrollLeft = maxScrollLeft;

    // Setup scroll boundary enforcement
    this.setupScrollBoundaryConstraints();

    // Setup responsive behavior
    this.setupTodayVsPreviousYearsResponsive();

    console.log('Today vs Previous Years scrolling setup complete:', {
      totalContentWidth,
      containerWidth,
      needsScrolling,
      rightPadding,
      maxScrollLeft,
      initialScrollLeft: this.scrollableContainer.scrollLeft
    });
  }

  /**
   * Calculate maximum scroll position ensuring rightmost column stays within grid boundaries
   */
  calculateMaxScrollWithRightPadding() {
    if (!this.scrollableContainer || !this.todayVsPreviousYearsScrollInfo) {
      return 0;
    }

    const {
      totalContentWidth,
      containerWidth,
      gridWidth,
      gridStartX,
      rightPadding
    } = this.todayVsPreviousYearsScrollInfo;

    const scrollWidth = this.scrollableContainer.scrollWidth;
    const clientWidth = this.scrollableContainer.clientWidth;

    // The key issue: Y-axis labels extend beyond the grid area
    // Grid area ends at: gridStartX + gridWidth
    // But Y-axis labels are positioned at: chartWidth + padding.right
    // We need to ensure columns stay within the actual grid boundaries

    // Calculate the effective right boundary for columns
    // This should be the grid right edge minus the column padding
    const gridRightEdge = gridStartX + gridWidth;
    const effectiveRightBoundary = gridRightEdge - rightPadding;

    // Calculate how much content extends beyond the container
    const contentOverflow = totalContentWidth - containerWidth;

    // The maximum scroll should position the rightmost column at the effective right boundary
    // Account for the space needed by Y-axis labels (which are outside the grid)
    const yAxisLabelsSpace = 48; // Grid inset space for Y-axis labels
    const maxScrollLeft = Math.max(0, contentOverflow - yAxisLabelsSpace);

    // Ensure we don't scroll beyond the natural scroll limit
    const naturalMaxScroll = Math.max(0, scrollWidth - clientWidth);

    // Use the more restrictive constraint
    return Math.min(maxScrollLeft, naturalMaxScroll);
  }

  /**
   * Calculate minimum scroll position ensuring leftmost column stays within grid boundaries
   */
  calculateMinScrollWithLeftPadding() {
    if (!this.scrollableContainer || !this.todayVsPreviousYearsScrollInfo) {
      return 0;
    }

    const { leftPadding } = this.todayVsPreviousYearsScrollInfo;

    // The leftmost column should not extend beyond the left boundary of the grid
    // Minimum scroll position should ensure leftmost content stays within grid + left padding
    const minScrollLeft = Math.max(0, -leftPadding);

    return minScrollLeft;
  }

  /**
   * Setup scroll boundary constraints to enforce grid boundaries
   */
  setupScrollBoundaryConstraints() {
    if (!this.scrollableContainer) {
      return;
    }

    // Store reference to this chart instance
    const chart = this;

    // Add scroll event listener to enforce both left and right boundaries
    const enforceScrollBoundaries = () => {
      const maxScrollLeft = chart.calculateMaxScrollWithRightPadding();
      const minScrollLeft = chart.calculateMinScrollWithLeftPadding();

      // Constrain scroll position to stay within grid boundaries
      if (chart.scrollableContainer.scrollLeft > maxScrollLeft) {
        chart.scrollableContainer.scrollLeft = maxScrollLeft;
      } else if (chart.scrollableContainer.scrollLeft < minScrollLeft) {
        chart.scrollableContainer.scrollLeft = minScrollLeft;
      }
    };

    // Add scroll event listener
    this.scrollableContainer.addEventListener('scroll', enforceScrollBoundaries);

    // Store reference for cleanup
    this.todayVsPreviousYearsScrollHandler = enforceScrollBoundaries;
  }

  /**
   * Setup responsive behavior for Today vs Previous Years chart
   */
  setupTodayVsPreviousYearsResponsive() {
    if (!this.scrollableContainer) return;

    // Store reference to this chart instance
    const chart = this;

    // Create resize observer to handle container width changes
    if (window.ResizeObserver) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          // Debounce resize events
          clearTimeout(chart.resizeTimeout);
          chart.resizeTimeout = setTimeout(() => {
            chart.handleTodayVsPreviousYearsResize();
          }, 100);
        }
      });

      resizeObserver.observe(this.scrollableContainer);
      this.todayVsPreviousYearsResizeObserver = resizeObserver;
    }

    // Fallback: window resize event
    const handleWindowResize = () => {
      clearTimeout(chart.resizeTimeout);
      chart.resizeTimeout = setTimeout(() => {
        chart.handleTodayVsPreviousYearsResize();
      }, 100);
    };

    window.addEventListener('resize', handleWindowResize);
    this.todayVsPreviousYearsWindowResizeHandler = handleWindowResize;
  }

  /**
   * Handle resize events for Today vs Previous Years chart with 32px right padding constraint
   */
  handleTodayVsPreviousYearsResize() {
    if (!this.scrollableContainer || !this.todayVsPreviousYearsScrollInfo) {
      return;
    }

    const newContainerWidth = this.scrollableContainer.clientWidth;
    const { totalContentWidth } = this.todayVsPreviousYearsScrollInfo;

    // Update scroll info
    this.todayVsPreviousYearsScrollInfo.containerWidth = newContainerWidth;
    this.todayVsPreviousYearsScrollInfo.needsScrolling = totalContentWidth > newContainerWidth;

    // Maintain right-anchored behavior with 32px padding constraint
    if (this.todayVsPreviousYearsScrollInfo.needsScrolling) {
      // Calculate maximum scroll position with 32px right padding constraint
      const maxScrollLeft = this.calculateMaxScrollWithRightPadding();
      this.scrollableContainer.scrollLeft = maxScrollLeft;
    } else {
      // Content fits, no scrolling needed
      this.scrollableContainer.scrollLeft = 0;
    }

    console.log('Today vs Previous Years resize handled:', {
      newContainerWidth,
      totalContentWidth,
      needsScrolling: this.todayVsPreviousYearsScrollInfo.needsScrolling,
      maxScrollWithPadding: this.calculateMaxScrollWithRightPadding(),
      scrollLeft: this.scrollableContainer.scrollLeft
    });
  }

  /**
   * Cleanup Today vs Previous Years chart event listeners and observers
   */
  cleanupTodayVsPreviousYearsChart() {
    // Remove scroll event listener
    if (this.scrollableContainer && this.todayVsPreviousYearsScrollHandler) {
      this.scrollableContainer.removeEventListener('scroll', this.todayVsPreviousYearsScrollHandler);
      this.todayVsPreviousYearsScrollHandler = null;
    }

    // Remove resize observer
    if (this.todayVsPreviousYearsResizeObserver) {
      this.todayVsPreviousYearsResizeObserver.disconnect();
      this.todayVsPreviousYearsResizeObserver = null;
    }

    // Remove window resize handler
    if (this.todayVsPreviousYearsWindowResizeHandler) {
      window.removeEventListener('resize', this.todayVsPreviousYearsWindowResizeHandler);
      this.todayVsPreviousYearsWindowResizeHandler = null;
    }

    // Clear scroll info
    this.todayVsPreviousYearsScrollInfo = null;
  }

  /**
   * Get canvas width for scrollable chart
   */
  getScrollableCanvasWidth() {
    // For scrollable charts, use the actual container width, not calculated width
    const containerRect = this.containerElement.getBoundingClientRect();
    const containerWidth = containerRect.width || 800; // Fallback to 800px

    return containerWidth;
  }
  
  /**
   * Draw grid lines (6 horizontal lines only) - Inset by 24px from axis labels
   */
  drawGridLines(parent, width, height, maxValue, startX = 0) {
    const gridLines = 5; // 5 intervals = 6 lines (including 0)
    const step = height / gridLines;
    
    // Grid lines are inset by 24px from axis labels on both sides
    for (let i = 0; i <= gridLines; i++) {
      const y = i * step;
      const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
      line.classList.add('snap-chart-grid-line');
      line.setAttribute('x1', startX);
      line.setAttribute('y1', y);
      line.setAttribute('x2', startX + width);
      line.setAttribute('y2', y);
      parent.appendChild(line);
    }
  }
  
  /**
   * Draw Y-axis labels flush to SVG edges with proper text alignment
   */
  drawYAxisLabels(parent, height, maxValue, side, xOffset = 0) {
    const steps = 5; // 5 intervals = 6 labels
    const stepValue = maxValue / steps;
    const stepHeight = height / steps;
    
    for (let i = 0; i <= steps; i++) {
      const value = Math.round((steps - i) * stepValue);
      const y = i * stepHeight;
      
      // Position labels flush to SVG edges
      let x;
      if (side === 'left') {
        // Left labels: positioned flush to left SVG edge with left text alignment
        x = xOffset;
      } else {
        // Right labels: positioned flush to right SVG edge with right text alignment
        x = xOffset;
      }
      
      this.createYAxisLabel(parent, value, x, y, side);
    }
  }
  
  /**
   * Create a Y-axis label element
   */
  createYAxisLabel(parent, value, x, y, side) {
    const label = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    label.classList.add('snap-chart-axis-label');
    if (side === 'right') label.classList.add('right');
    label.setAttribute('x', x);
    label.setAttribute('y', y);
    label.textContent = side === 'right' ? `$${value}` : value;
    
    parent.appendChild(label);
  }
  
  /**
   * Draw a single comparison column (not stacked)
   */
  drawComparisonColumn(parent, dataPoint, x, width, chartHeight, salesScale, index) {
    const totalHeight = dataPoint.sales * salesScale;

    const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    rect.classList.add('snap-chart-comparison-segment');
    rect.setAttribute('x', x);
    rect.setAttribute('y', chartHeight - totalHeight);
    rect.setAttribute('width', width);
    rect.setAttribute('height', totalHeight);
    // Rounded corners all around for comparison columns
    rect.setAttribute('rx', '4');
    rect.setAttribute('ry', '4');

    // Ensure consistent comparison column styling across all chart types
    // The CSS class .snap-chart-comparison-segment handles the color and opacity

    parent.appendChild(rect);
    return rect;
  }

  /**
   * Helper: Create SVG path for a rectangle with per-corner radii
   * corners: {tl, tr, br, bl} (each 0 or radius)
   */
  createRoundedRectPath(x, y, w, h, corners) {
    const r = corners;
    // Clamp radii to not exceed half width/height
    const rtl = Math.min(r.tl || 0, w / 2, h / 2);
    const rtr = Math.min(r.tr || 0, w / 2, h / 2);
    const rbr = Math.min(r.br || 0, w / 2, h / 2);
    const rbl = Math.min(r.bl || 0, w / 2, h / 2);
    return [
      `M${x + rtl},${y}`,
      `H${x + w - rtr}`,
      rtr ? `A${rtr},${rtr} 0 0 1 ${x + w},${y + rtr}` : `L${x + w},${y}`,
      `V${y + h - rbr}`,
      rbr ? `A${rbr},${rbr} 0 0 1 ${x + w - rbr},${y + h}` : `L${x + w},${y + h}`,
      `H${x + rbl}`,
      rbl ? `A${rbl},${rbl} 0 0 1 ${x},${y + h - rbl}` : `L${x},${y + h}`,
      `V${y + rtl}`,
      rtl ? `A${rtl},${rtl} 0 0 1 ${x + rtl},${y}` : `L${x},${y}`,
      'Z'
    ].join(' ');
  }

  /**
   * Draw a stacked column
   */
  drawStackedColumn(parent, dataPoint, x, width, chartHeight, salesScale, index, isComparison = false) {
    const columnGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    columnGroup.classList.add('snap-chart-column-group');
    columnGroup.setAttribute('data-index', index);

    if (isComparison) {
      columnGroup.classList.add('snap-chart-comparison');
    }

    // Filter out zero-value segments to get only visible segments
    const visibleSegments = dataPoint.values
      .map((value, originalIndex) => ({ value, originalIndex }))
      .filter(segment => segment.value > 0);

    const numVisibleSegments = visibleSegments.length;
    const GAP_SIZE = 0; // No gaps between segments - flush stacking
    const MIN_SEGMENT_HEIGHT = 6; // Minimum height for very small segments to ensure visibility and clickability

    // Calculate total gaps that will be used
    const totalGaps = Math.max(0, numVisibleSegments - 1) * GAP_SIZE;

    // Calculate the total data value for this column
    const totalDataValue = visibleSegments.reduce((sum, segment) => sum + segment.value, 0);

    // Calculate the total available height for segments (excluding gaps)
    const totalAvailableHeight = totalDataValue * salesScale;
    const totalSegmentHeight = totalAvailableHeight - totalGaps;

    // Calculate adjusted scale that accounts for gaps
    // This ensures total column height (segments + gaps) equals the exact grid value
    const adjustedSalesScale = totalSegmentHeight / totalDataValue;

    // Start from the bottom of the chart
    let currentY = chartHeight;
    let actualColumnHeight = 0; // Track the actual rendered column height for text positioning

    visibleSegments.forEach((segment, visibleIndex) => {
      const { value, originalIndex } = segment;

      // Calculate the adjusted segment height that accounts for gaps
      let segmentHeight = value * adjustedSalesScale;

      // Apply minimum height only to very small segments (< 4px) to preserve proportions
      if (segmentHeight < 4) {
        segmentHeight = MIN_SEGMENT_HEIGHT;
      }

      // Track actual column height including gaps
      actualColumnHeight += segmentHeight;
      if (visibleIndex < numVisibleSegments - 1) {
        actualColumnHeight += GAP_SIZE;
      }

      // Position segments with exactly 1px gap between them
      // First visible segment starts at the bottom with no gap
      // Each subsequent visible segment has a 1px gap above the previous one
      const segmentY = currentY - segmentHeight;

      // Move to the next segment position (accounting for the gap)
      currentY = segmentY - (visibleIndex < numVisibleSegments - 1 ? GAP_SIZE : 0);

      let shape;
      // Check if this is a Today vs Previous Years chart for full rounded corners
      const isTodayVsChart = this.options && this.options.isTodayVsPreviousYearsChart;

      // Main columns: use path for top/bottom/single, rect for middle
      if (!isComparison && numVisibleSegments > 1) {
        if (visibleIndex === 0) {
          // Bottom visible segment: round BOTTOM corners only (top sharp)
          shape = document.createElementNS('http://www.w3.org/2000/svg', 'path');
          shape.setAttribute('d', this.createRoundedRectPath(x, segmentY, width, segmentHeight, { tl: 0, tr: 0, br: 4, bl: 4 }));
        } else if (visibleIndex === numVisibleSegments - 1) {
          // Top visible segment: round TOP corners only (bottom sharp)
          shape = document.createElementNS('http://www.w3.org/2000/svg', 'path');
          shape.setAttribute('d', this.createRoundedRectPath(x, segmentY, width, segmentHeight, { tl: 4, tr: 4, br: 0, bl: 0 }));
        } else {
          // Middle visible segments: sharp
          shape = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
          shape.setAttribute('x', x);
          shape.setAttribute('y', segmentY);
          shape.setAttribute('width', width);
          shape.setAttribute('height', segmentHeight);
        }
      } else if (!isComparison && numVisibleSegments === 1) {
        // Single visible segment: all corners rounded
        shape = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        shape.setAttribute('d', this.createRoundedRectPath(x, segmentY, width, segmentHeight, { tl: 4, tr: 4, br: 4, bl: 4 }));
      } else if (isComparison) {
        // Comparison segments: always round all corners (rect)
        shape = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        shape.setAttribute('x', x);
        shape.setAttribute('y', segmentY);
        shape.setAttribute('width', width);
        shape.setAttribute('height', segmentHeight);
        shape.setAttribute('rx', '4');
        shape.setAttribute('ry', '4');
      }

      shape.classList.add('snap-chart-column-segment');
      if (isComparison) {
        shape.classList.add('snap-chart-comparison-segment');
      } else {
        // Apply custom color logic for Today vs Previous Years chart
        if (dataPoint.isCurrentYear !== undefined) {
          if (dataPoint.isCurrentYear) {
            // Check if single marketplace is selected for current year columns
            const isMarketplaceFocusActive = window.globalMarketplaceFocus && window.globalMarketplaceFocus !== 'all';

            if (isMarketplaceFocusActive) {
              // Use the specific marketplace color when single marketplace is selected
              const marketplaceCode = window.globalMarketplaceFocus.toUpperCase();
              const marketplaceMapping = this.marketplaceMapping[marketplaceCode];

              if (marketplaceMapping) {
                shape.style.fill = marketplaceMapping.color;
                shape.style.fillOpacity = '0.6';
                shape.style.stroke = marketplaceMapping.color;
                shape.style.strokeWidth = '0.75px';
              } else {
                // Fallback to default current year color
                shape.style.fill = '#6033FF';
                shape.style.fillOpacity = '0.6';
                shape.style.stroke = '#6033FF';
                shape.style.strokeWidth = '0.75px';
              }
            } else {
              // Default current year color when all marketplaces are shown
              shape.style.fill = '#6033FF';
              shape.style.fillOpacity = '0.6';
              shape.style.stroke = '#6033FF';
              shape.style.strokeWidth = '0.75px';
            }
          } else {
            // Use standard comparison column color for consistency
            shape.style.fill = '#606F95';
            shape.style.fillOpacity = '0.6';
            shape.style.stroke = '#606F95';
            shape.style.strokeWidth = '0.75px';
          }
        } else {
          // Check if single marketplace is selected (marketplace focus mode)
          const isMarketplaceFocusActive = window.globalMarketplaceFocus && window.globalMarketplaceFocus !== 'all';

          if (isMarketplaceFocusActive) {
            // Use the specific marketplace color when single marketplace is selected
            const marketplaceCode = window.globalMarketplaceFocus.toUpperCase();
            const marketplaceMapping = this.marketplaceMapping[marketplaceCode];

            if (marketplaceMapping) {
              // Apply the marketplace-specific color directly
              shape.style.fill = marketplaceMapping.color;
              shape.style.fillOpacity = '0.6';
              shape.style.stroke = marketplaceMapping.color;
              shape.style.strokeWidth = '0.75px';
            } else {
              // Fallback to default color if mapping not found
              shape.classList.add(`snap-chart-color-${originalIndex + 1}`);
            }
          } else {
            // Use original index for color assignment to maintain consistent marketplace colors
            shape.classList.add(`snap-chart-color-${originalIndex + 1}`);
          }
        }
      }

      // Add animation (disabled for scrollable stacked columns for performance)
      if (this.options.animate && !isComparison && this.type !== 'scrollable-stacked-column') {
        shape.classList.add('snap-chart-column-animate');
        shape.style.animationDelay = `${index * 0.05}s`;

        // Clean up will-change after animation completes
        const animationDuration = 600 + (index * 50); // 600ms + delay
        setTimeout(() => {
          if (shape) {
            shape.classList.add('animation-complete');
          }
        }, animationDuration + 100);
      }

      columnGroup.appendChild(shape);
    });
    

    
    // Add invisible rectangle to cover the entire column vertical space for consistent hover
    // This ensures hover works even when mouse is over empty areas within the column
    // Apply to both main and comparison columns for consistent behavior
    const hoverArea = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    hoverArea.classList.add('snap-chart-column-hover-area');
    if (isComparison) {
      hoverArea.classList.add('snap-chart-comparison-hover-area');
    }
    hoverArea.setAttribute('x', x);
    hoverArea.setAttribute('y', 0); // Start from top of chart
    hoverArea.setAttribute('width', width);
    hoverArea.setAttribute('height', chartHeight); // Cover full vertical space
    hoverArea.setAttribute('fill', 'transparent');
    hoverArea.setAttribute('pointer-events', 'all');

    // Add this as the first child so it's below all segments
    // but still captures mouse events
    columnGroup.insertBefore(hoverArea, columnGroup.firstChild);
    
    // Add hover events to both main and comparison columns
    if (!isComparison) {
      this.addColumnEvents(columnGroup, dataPoint, index);
    } else {
      // For comparison columns, add hover events that trigger the same tooltip as current columns
      this.addComparisonColumnEvents(columnGroup, dataPoint, index);
    }
    
    parent.appendChild(columnGroup);
    return { columnGroup, actualHeight: actualColumnHeight };
  }



  /**
   * Draw two-line labels for Today vs Previous Years chart
   */
  drawTodayVsPreviousYearsLabels(parent, columnAreaStartX, columnWidth, gapBetweenColumns, chartHeight) {
    const N = this.data.length;
    const labelGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    labelGroup.classList.add('snap-chart-labels-group');

    for (let i = 0; i < N; i++) {
      const dataPoint = this.data[i];
      const columnX = columnAreaStartX + (i * (columnWidth + gapBetweenColumns));
      const centerX = columnX + (columnWidth / 2);

      // First line: "Month Day" format (e.g., "Jul 26")
      const firstLineText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      firstLineText.setAttribute('x', centerX);
      firstLineText.setAttribute('y', chartHeight + 20);
      firstLineText.setAttribute('text-anchor', 'middle');
      firstLineText.setAttribute('font-family', 'Inter, -apple-system, BlinkMacSystemFont, sans-serif');
      firstLineText.setAttribute('font-size', '11px');
      firstLineText.setAttribute('font-weight', '400');
      firstLineText.setAttribute('fill', '#666666');
      firstLineText.textContent = dataPoint.monthDay;

      // Second line: Year in abbreviated format (e.g., "'25")
      const secondLineText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      secondLineText.setAttribute('x', centerX);
      secondLineText.setAttribute('y', chartHeight + 35);
      secondLineText.setAttribute('text-anchor', 'middle');
      secondLineText.setAttribute('font-family', 'Inter, -apple-system, BlinkMacSystemFont, sans-serif');
      secondLineText.setAttribute('font-size', '11px');
      secondLineText.setAttribute('font-weight', '400');
      secondLineText.setAttribute('fill', '#666666');
      secondLineText.textContent = dataPoint.yearLabel;

      labelGroup.appendChild(firstLineText);
      labelGroup.appendChild(secondLineText);
    }

    parent.appendChild(labelGroup);
  }

  /**
   * Draw a single column for daily sales history with returns support
   */
  drawSingleColumn(parent, dataPoint, x, width, chartHeight, salesScale, index, returnsScale = null, baselineY = null) {
    const columnGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    columnGroup.classList.add('snap-chart-column-group');
    columnGroup.setAttribute('data-index', index);
    
    // Add no-hover class for charts with more than 180 columns to remove cursor
    if (this.data && this.data.length > 180) {
      columnGroup.classList.add('snap-chart-no-hover');
    }
    
    // Get data values with defaults
    const sales = dataPoint.sales || 0;
    const returns = dataPoint.returns || 0;
    const royalties = dataPoint.royalties || 0;
    
    // Use baseline positioning if provided (for daily sales with returns)
    const actualBaselineY = baselineY !== null ? baselineY : chartHeight;
    const actualReturnsScale = returnsScale !== null ? returnsScale : salesScale;
    
    // Calculate heights and positions
    const salesHeight = sales * salesScale;
    
    // Cap returns height to prevent overlap with date labels (max 35px below baseline)
    const maxReturnsHeight = 35; // Maximum height for returns segments to prevent overlap
    const calculatedReturnsHeight = returns * actualReturnsScale;
    const returnsHeight = Math.min(calculatedReturnsHeight, maxReturnsHeight);
    
    // Position sales column starting from baseline (0 line) going upward
    const salesY = actualBaselineY - salesHeight;
    
    // Position returns segment starting from baseline (0 line) going downward
    const returnsY = actualBaselineY;
    
    // Determine which scenario we're in:
    // 1. Day with sales/royalties (and possibly returns)
    // 2. Day with sales/royalties/returns  
    // 3. Day with returns only
    // 4. Day with no sales/royalties/returns
    
    const hasSales = sales > 0;
    const hasReturns = returns > 0;
    const hasAnyData = hasSales || hasReturns || royalties > 0;
    
    if (hasSales) {
      // Scenario 1 & 2: Day with sales (and possibly returns)
      if (width > 2) {
        // Create path for top-rounded rectangle
        const salesPath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
        salesPath.classList.add('snap-chart-single-column');

        // Apply custom color logic for Today vs Previous Years chart
        if (dataPoint.isCurrentYear !== undefined) {
          if (dataPoint.isCurrentYear) {
            // Check if single marketplace is selected for current year columns
            const isMarketplaceFocusActive = window.globalMarketplaceFocus && window.globalMarketplaceFocus !== 'all';

            if (isMarketplaceFocusActive) {
              // Use the specific marketplace color when single marketplace is selected
              const marketplaceCode = window.globalMarketplaceFocus.toUpperCase();
              const marketplaceMapping = this.marketplaceMapping[marketplaceCode];

              if (marketplaceMapping) {
                salesPath.style.fill = marketplaceMapping.color;
                salesPath.style.fillOpacity = '0.6';
                salesPath.style.stroke = marketplaceMapping.color;
                salesPath.style.strokeWidth = '0.75px';
              } else {
                // Fallback to default current year color
                salesPath.style.fill = '#6033FF';
                salesPath.style.fillOpacity = '0.6';
                salesPath.style.stroke = '#6033FF';
                salesPath.style.strokeWidth = '0.75px';
              }
            } else {
              // Default current year color when all marketplaces are shown
              salesPath.style.fill = '#6033FF';
              salesPath.style.fillOpacity = '0.6';
              salesPath.style.stroke = '#6033FF';
              salesPath.style.strokeWidth = '0.75px';
            }
          } else {
            // Use standard comparison column color for consistency
            salesPath.style.fill = '#606F95';
            salesPath.style.fillOpacity = '0.6';
            salesPath.style.stroke = '#606F95';
            salesPath.style.strokeWidth = '0.75px';
          }
        } else {
          // Default color for regular daily sales history
          salesPath.classList.add('snap-chart-color-1');
        }
        
        // Create path for rectangle with top rounded corners only
        const radius = 2;
        const pathData = `
          M ${x + radius} ${salesY}
          L ${x + width - radius} ${salesY}
          Q ${x + width} ${salesY} ${x + width} ${salesY + radius}
          L ${x + width} ${salesY + salesHeight}
          L ${x} ${salesY + salesHeight}
          L ${x} ${salesY + radius}
          Q ${x} ${salesY} ${x + radius} ${salesY}
          Z
        `.replace(/\s+/g, ' ').trim();
        
        salesPath.setAttribute('d', pathData);
        columnGroup.appendChild(salesPath);
      } else {
        // For narrow columns, use regular rectangle
        const salesRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
        salesRect.classList.add('snap-chart-single-column');

        // Apply custom color logic for Today vs Previous Years chart
        if (dataPoint.isCurrentYear !== undefined) {
          if (dataPoint.isCurrentYear) {
            // Check if single marketplace is selected for current year columns
            const isMarketplaceFocusActive = window.globalMarketplaceFocus && window.globalMarketplaceFocus !== 'all';

            if (isMarketplaceFocusActive) {
              // Use the specific marketplace color when single marketplace is selected
              const marketplaceCode = window.globalMarketplaceFocus.toUpperCase();
              const marketplaceMapping = this.marketplaceMapping[marketplaceCode];

              if (marketplaceMapping) {
                salesRect.style.fill = marketplaceMapping.color;
                salesRect.style.fillOpacity = '0.6';
                salesRect.style.stroke = marketplaceMapping.color;
                salesRect.style.strokeWidth = '0.75px';
              } else {
                // Fallback to default current year color
                salesRect.style.fill = '#6033FF';
                salesRect.style.fillOpacity = '0.6';
                salesRect.style.stroke = '#6033FF';
                salesRect.style.strokeWidth = '0.75px';
              }
            } else {
              // Default current year color when all marketplaces are shown
              salesRect.style.fill = '#6033FF';
              salesRect.style.fillOpacity = '0.6';
              salesRect.style.stroke = '#6033FF';
              salesRect.style.strokeWidth = '0.75px';
            }
          } else {
            // Use standard comparison column color for consistency
            salesRect.style.fill = '#606F95';
            salesRect.style.fillOpacity = '0.6';
            salesRect.style.stroke = '#606F95';
            salesRect.style.strokeWidth = '0.75px';
          }
        } else {
          // Default color for regular daily sales history
          salesRect.classList.add('snap-chart-color-1');
        }

        salesRect.setAttribute('x', x);
        salesRect.setAttribute('y', salesY);
        salesRect.setAttribute('width', width);
        salesRect.setAttribute('height', salesHeight);
        columnGroup.appendChild(salesRect);
      }
    }
    
    if (hasReturns) {
      // Scenario 2 & 3: Day with returns (with or without sales)
      const returnsRect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      returnsRect.classList.add('snap-chart-returns-segment');
      
      // Limit returns column width to maximum 4px
      const maxReturnsWidth = 4;
      const returnsWidth = Math.min(width, maxReturnsWidth);
      
      // Center the returns segment within the column if it's narrower than the column
      const returnsX = width > maxReturnsWidth ? x + (width - returnsWidth) / 2 : x;
      
      returnsRect.setAttribute('x', returnsX);
      returnsRect.setAttribute('y', returnsY);
      returnsRect.setAttribute('width', returnsWidth);
      returnsRect.setAttribute('height', returnsHeight);
      returnsRect.setAttribute('fill', '#FF391F'); // Returns color as specified
      
      // Returns segments should be completely sharp (no rounded corners)
      // No rounded corners for returns segments
      
      columnGroup.appendChild(returnsRect);
    }
    
    if (!hasAnyData) {
      // Scenario 4: Day with no sales/royalties/returns - show stub
      const stubHeight = 4;
      const stub = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      stub.classList.add('snap-chart-zero-sales-stub');
      stub.setAttribute('x', x);
      stub.setAttribute('y', actualBaselineY - stubHeight);
      stub.setAttribute('width', width);
      stub.setAttribute('height', stubHeight);
      columnGroup.appendChild(stub);
    }
    
    // No animation for daily sales history chart - too many columns would cause performance issues
    
    // Hover events will be added by the calling method based on column count strategy
    
    parent.appendChild(columnGroup);
    return columnGroup;
  }

  /**
   * Draw royalties line (straight line segments with subtle rounded corners)
   */
  drawRoyaltiesLine(parent, points, isComparison = false) {
    if (points.length < 2) return;
    
    // Use straight line segments with subtle rounded corners for all chart types
    let pathData = `M ${points[0].x} ${points[0].y}`;
    
    // Draw straight lines with subtle rounded corners at joints
    for (let i = 1; i < points.length; i++) {
      const currentPoint = points[i];
      const prevPoint = points[i - 1];
      const nextPoint = points[i + 1];
      
      if (i === points.length - 1) {
        // Last point - straight line
        pathData += ` L ${currentPoint.x} ${currentPoint.y}`;
      } else {
        // Add subtle rounded corner at the joint
        const cornerRadius = 1.5; // Small radius for subtle rounding
        
        // Calculate the direction vectors
        const dx1 = currentPoint.x - prevPoint.x;
        const dy1 = currentPoint.y - prevPoint.y;
        const dx2 = nextPoint.x - currentPoint.x;
        const dy2 = nextPoint.y - currentPoint.y;
        
        // Normalize the vectors
        const len1 = Math.sqrt(dx1 * dx1 + dy1 * dy1);
        const len2 = Math.sqrt(dx2 * dx2 + dy2 * dy2);
        
        if (len1 > 0 && len2 > 0) {
          const ux1 = dx1 / len1;
          const uy1 = dy1 / len1;
          const ux2 = dx2 / len2;
          const uy2 = dy2 / len2;
          
          // Calculate corner points
          const cornerStart = Math.min(cornerRadius, len1 / 2);
          const cornerEnd = Math.min(cornerRadius, len2 / 2);
          
          const startX = currentPoint.x - ux1 * cornerStart;
          const startY = currentPoint.y - uy1 * cornerStart;
          const endX = currentPoint.x + ux2 * cornerEnd;
          const endY = currentPoint.y + uy2 * cornerEnd;
          
          // Draw line to corner start, then subtle rounded corner, then continue
          pathData += ` L ${startX} ${startY}`;
          pathData += ` Q ${currentPoint.x} ${currentPoint.y} ${endX} ${endY}`;
        } else {
          // Fallback to straight line
          pathData += ` L ${currentPoint.x} ${currentPoint.y}`;
        }
      }
    }
    // Draw line
    const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    path.setAttribute('d', pathData);
    path.classList.add('snap-chart-royalties-line');
    if (isComparison) {
      path.classList.add('snap-chart-comparison-line');
      parent.appendChild(path); // Add comparison line without animation
    } else if (this.options.animate) {
      // Calculate actual path length for dynamic animation
      parent.appendChild(path); // Add to DOM first so we can measure
      const pathLength = path.getTotalLength();
      
      // Set dynamic stroke-dasharray and stroke-dashoffset
      path.style.strokeDasharray = pathLength;
      path.style.strokeDashoffset = pathLength;
      
      // Start animation
      path.style.animation = 'none'; // Remove CSS animation
      path.style.transition = 'stroke-dashoffset 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
      
      // Trigger animation with delay
      setTimeout(() => {
        if (path) {
          path.style.strokeDashoffset = '0';
        }
      }, 300); // Match original animation delay
      
      // Clean up after animation
      setTimeout(() => {
        if (path) {
          path.classList.add('animation-complete');
          path.style.strokeDasharray = 'none';
          path.style.strokeDashoffset = 'none';
          path.style.transition = 'none';
        }
      }, 1100);
    } else {
      parent.appendChild(path);
    }
    
    // Store points for later dot creation (only for main data, not comparison)
    if (!isComparison) {
      this.royaltiesPoints = points;
    }
  }
  
  /**
   * Add royalties dots on top of all other chart elements
   */
  addRoyaltiesDots(parent) {
    if (!this.royaltiesPoints || this.royaltiesPoints.length === 0) return;
    
    this.royaltiesPoints.forEach((point, index) => {
      const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
      circle.setAttribute('cx', point.x);
      circle.setAttribute('cy', point.y);
      circle.setAttribute('r', '3');
      circle.classList.add('snap-chart-royalties-dot');
      circle.setAttribute('data-column-index', point.index);
      parent.appendChild(circle);
    });
  }
  
  /**
   * Group columns by timeframe (day, month, quarter, year) based on chart type and time span
   */
  groupColumnsByTimeframe() {
    if (!this.data || this.data.length === 0) return [];
    
    const groups = [];
    
    // Chart-type specific grouping strategy
    if (this.type === 'stacked-column') {
      // Stacked columns with compare: always show days
      this.groupByDay(groups);
    } else if (this.type === 'scrollable-stacked-column') {
      // Scrollable stacked columns: always show months
      this.groupByMonth(groups);
    } else {
      // Other chart types (like daily-sales-history): use time span logic
      const timeSpanInMonths = this.calculateTimeSpanInMonths();
      
      if (timeSpanInMonths <= 18) {
        // Group by month for short spans
        this.groupByMonth(groups);
      } else if (timeSpanInMonths <= 96) {
        // Group by quarter for medium spans
        this.groupByQuarter(groups);
      } else {
        // Group by year for long spans
        this.groupByYear(groups);
      }
    }
    
    return groups;
  }
  
  /**
   * Group columns by day
   */
  groupByDay(groups) {
    this.data.forEach((dataPoint, index) => {
      // For stacked columns, each data point represents a day
      const month = dataPoint.month || 'MMM';
      const day = dataPoint.day || 'DD';
      const year = dataPoint.year || 'YY';
      const dayKey = `${month}-${day}-${year}`;
      
      const dayGroup = {
        key: dayKey,
        type: 'day',
        label: `${month}, ${day}`,  // Format: "OCT, 15"
        sublabel: `'${year}`,       // Format: "'24"
        startIndex: index,
        endIndex: index,
        dataPoints: [dataPoint]
      };
      groups.push(dayGroup);
    });
  }
  
  /**
   * Group columns by month
   */
  groupByMonth(groups) {
    let currentGroup = null;
    
    this.data.forEach((dataPoint, index) => {
      // Handle both regular data and daily sales history data
      let monthKey, month, year;
      
      if (dataPoint.dateObj) {
        // Daily sales history - use dateObj
        month = dataPoint.dateObj.toLocaleDateString('en-US', { month: 'short' }).toUpperCase();
        year = dataPoint.dateObj.getFullYear().toString().slice(-2);
        monthKey = `${month}-${year}`;
      } else {
        // Regular stacked column data
        month = dataPoint.month;
        year = dataPoint.year;
        monthKey = `${month}-${year}`;
      }
      
      if (!currentGroup || currentGroup.key !== monthKey) {
        // Start new group
        if (currentGroup) {
          currentGroup.endIndex = index - 1;
        }
        
        currentGroup = {
          key: monthKey,
          type: 'month',
          label: month,
          sublabel: year ? `'${year}` : '',
          startIndex: index,
          endIndex: index,
          dataPoints: []
        };
        groups.push(currentGroup);
      }
      
      currentGroup.dataPoints.push(dataPoint);
      currentGroup.endIndex = index;
    });
  }
  
  /**
   * Group columns by quarter
   */
  groupByQuarter(groups) {
    let currentGroup = null;
    
    this.data.forEach((dataPoint, index) => {
      // Handle both regular data and daily sales history data
      let quarter, year, quarterKey;
      
      if (dataPoint.dateObj) {
        // Daily sales history - use dateObj
        const monthIndex = dataPoint.dateObj.getMonth();
        quarter = Math.floor(monthIndex / 3) + 1;
        year = dataPoint.dateObj.getFullYear().toString().slice(-2);
        quarterKey = `${year}-Q${quarter}`;
      } else {
        // Regular stacked column data
        const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
        const monthIndex = monthNames.indexOf(dataPoint.month);
        quarter = monthIndex !== -1 ? Math.floor(monthIndex / 3) + 1 : 1;
        year = dataPoint.year;
        quarterKey = `${year}-Q${quarter}`;
      }
      
      if (!currentGroup || currentGroup.key !== quarterKey) {
        // Start new group
        if (currentGroup) {
          currentGroup.endIndex = index - 1;
        }
        
        currentGroup = {
          key: quarterKey,
          type: 'quarter',
          label: year ? `'${year}` : '',
          sublabel: `Q${quarter}`,
          startIndex: index,
          endIndex: index,
          dataPoints: []
        };
        groups.push(currentGroup);
      }
      
      currentGroup.dataPoints.push(dataPoint);
      currentGroup.endIndex = index;
    });
  }
  
  /**
   * Group columns by year
   */
  groupByYear(groups) {
    let currentGroup = null;
    
    this.data.forEach((dataPoint, index) => {
      // Handle both regular data and daily sales history data
      let yearKey, year;
      
      if (dataPoint.dateObj) {
        // Daily sales history - use dateObj
        year = dataPoint.dateObj.getFullYear().toString().slice(-2);
        yearKey = year;
      } else {
        // Regular stacked column data
        year = dataPoint.year;
        yearKey = year;
      }
      
      if (!currentGroup || currentGroup.key !== yearKey) {
        // Start new group
        if (currentGroup) {
          currentGroup.endIndex = index - 1;
        }
        
        currentGroup = {
          key: yearKey,
          type: 'year',
          label: year ? `'${year}` : '',
          sublabel: '',
          startIndex: index,
          endIndex: index,
          dataPoints: []
        };
        groups.push(currentGroup);
      }
      
      currentGroup.dataPoints.push(dataPoint);
      currentGroup.endIndex = index;
    });
  }
  
  /**
   * Calculate actual column positions for daily sales history
   */
  calculateActualColumnPositions(startX, columnWidth, gapBetweenColumns, numColumns) {
    const positions = [];
    for (let i = 0; i < numColumns; i++) {
      const columnX = startX + (i * (columnWidth + gapBetweenColumns));
      positions.push(columnX);
    }
    return positions;
  }
  
  /**
   * Calculate the calendar start date for a timeframe group
   */
  calculateGroupCalendarStartDate(group) {
    if (!group || !group.dataPoints || group.dataPoints.length === 0) return null;
    
    const firstDataPoint = group.dataPoints[0];
    
    if (group.type === 'month') {
      // For month groups, use the 1st of the month
      if (firstDataPoint.dateObj) {
        return new Date(firstDataPoint.dateObj.getFullYear(), firstDataPoint.dateObj.getMonth(), 1);
      }
    } else if (group.type === 'quarter') {
      // For quarter groups, use the 1st of the quarter's first month
      if (firstDataPoint.dateObj) {
        const month = firstDataPoint.dateObj.getMonth();
        const quarterStartMonth = Math.floor(month / 3) * 3; // 0, 3, 6, or 9
        return new Date(firstDataPoint.dateObj.getFullYear(), quarterStartMonth, 1);
      }
    } else if (group.type === 'year') {
      // For year groups, use January 1st of the year
      if (firstDataPoint.dateObj) {
        return new Date(firstDataPoint.dateObj.getFullYear(), 0, 1);
      }
    }
    
    return null;
  }
  
  /**
   * Calculate the start position for a group of columns
   */
  calculateGroupStartPosition(group, positioningInfo) {
    const { sidePadding, columnGroupWidth, gapBetweenGroups, columnAreaStartX, columnWidth, gapBetweenColumns, startX, isDailySales, actualColumnPositions, dateRange } = positioningInfo;
    
    // Calculate position based on chart type
    if (this.type === 'stacked-column') {
      // For stacked columns, use the unified positioning system
      return sidePadding + (group.startIndex * (columnGroupWidth + gapBetweenGroups));
    } else if (this.type === 'scrollable-stacked-column') {
      // For scrollable columns
      return columnAreaStartX + (group.startIndex * (columnWidth + gapBetweenColumns));
    } else if (this.type === 'daily-sales-history') {
      // For daily sales history, use time-based positioning for calendar accuracy
      if (isDailySales && dateRange) {
        const groupCalendarStart = this.calculateGroupCalendarStartDate(group);
        if (groupCalendarStart) {
          // Use time-based positioning like drawMonthLabelsFromDateRange
          const totalTimeSpan = dateRange.endDate.getTime() - dateRange.startDate.getTime();
          const timeProgress = (groupCalendarStart.getTime() - dateRange.startDate.getTime()) / totalTimeSpan;
          
          // Position based on time progression within the chart area
          const FIXED_PADDING = 32; // Same padding used in daily sales history
          const availableWidth = dateRange.chartWidth - (2 * FIXED_PADDING);
          const labelAreaStartX = dateRange.chartStartX + FIXED_PADDING;
          
          return labelAreaStartX + (timeProgress * availableWidth);
        }
      }
      
      // Fallback to data-index-based positioning
      if (isDailySales && actualColumnPositions && actualColumnPositions[group.startIndex] !== undefined) {
        return actualColumnPositions[group.startIndex];
      }
      return startX + (group.startIndex * (columnWidth + gapBetweenColumns));
    }
    
    return 0;
  }
  
  /**
   * Draw group labels positioned centered under the first column of each timeframe group
   */
  drawGroupLabels(parent, groups, positioningInfo, chartHeight) {
    if (!groups || groups.length === 0) return;
    
    groups.forEach(group => {
      const groupStartX = this.calculateGroupStartPosition(group, positioningInfo);
      
      // Position labels centered under the first column of the group
      // Use groupStartX + (columnWidth / 2) for centering
      const columnWidth = positioningInfo.columnWidth || positioningInfo.columnGroupWidth || 24; // Fallback width
      const labelX = groupStartX + (columnWidth / 2);
      
      // Primary label (month, year, or quarter info)
      if (group.label) {
        const primaryLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        primaryLabel.classList.add('snap-chart-column-label');
        primaryLabel.setAttribute('x', labelX);
        primaryLabel.setAttribute('y', chartHeight + 20);
        primaryLabel.setAttribute('text-anchor', 'middle'); // Center under first column
        primaryLabel.textContent = group.label;
        
        // Only add clickable functionality for daily-sales-history charts
        if (this.type === 'daily-sales-history') {
          primaryLabel.classList.add('snap-chart-clickable-label');
          primaryLabel.style.cursor = 'pointer';
          
          // Add click event for filtering
          primaryLabel.addEventListener('click', (e) => {
            e.stopPropagation();
            this.filterToTimeframe(group);
          });
        }
        
        parent.appendChild(primaryLabel);
      }
      
      // Secondary label (year, quarter, or additional info)
      if (group.sublabel) {
        const secondaryLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        secondaryLabel.classList.add('snap-chart-column-label');
        secondaryLabel.setAttribute('x', labelX);
        secondaryLabel.setAttribute('y', chartHeight + 35);
        secondaryLabel.setAttribute('text-anchor', 'middle'); // Center under first column
        secondaryLabel.textContent = group.sublabel;
        
        // Only add clickable functionality for daily-sales-history charts
        if (this.type === 'daily-sales-history') {
          secondaryLabel.classList.add('snap-chart-clickable-label');
          secondaryLabel.style.cursor = 'pointer';
          
          // Add click event for filtering
          secondaryLabel.addEventListener('click', (e) => {
            e.stopPropagation();
            this.filterToTimeframe(group);
          });
        }
        
        parent.appendChild(secondaryLabel);
      }
    });
  }
  
  /**
   * Draw column labels - chart type specific (LEGACY - now uses grouped labels)
   */
  drawColumnLabels(parent, dataPoint, x, width, chartHeight, salesScale, actualColumnHeight = null) {
    const centerX = x + width / 2;
    
    // Chart-type specific labeling
    if (this.type === 'stacked-column' || this.type === 'scrollable-stacked-column') {
      // NOTE: Individual column labels are now replaced by grouped labels
      // This method is kept for compatibility but labels are drawn by drawGroupLabels()
      
      // Only draw sales value labels above columns (not time labels)
      // Skip time labels - they are now handled by drawGroupLabels()
    } else {
      // Daily sales history and other charts: Use existing time-span based logic
      const showMonthLabels = this.shouldShowMonthLabels();
      
      if (showMonthLabels) {
        // Show months for data spans of 18 months or less
        // Month label (as per Figma: "JAN")
        if (dataPoint.month) {
          const monthLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
          monthLabel.classList.add('snap-chart-column-label');
          monthLabel.setAttribute('x', centerX);
          monthLabel.setAttribute('y', chartHeight + 20);
          monthLabel.textContent = dataPoint.month;
          parent.appendChild(monthLabel);
        }
        
        // Year label below month (show full year or abbreviated)
        const yearLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        yearLabel.classList.add('snap-chart-column-label');
        yearLabel.setAttribute('x', centerX);
        yearLabel.setAttribute('y', chartHeight + 35);
        
        // Use actual year from data if available, otherwise default to current year
        if (dataPoint.year) {
          // If year is 2-digit, add apostrophe (e.g., '25)
          const yearText = dataPoint.year.length === 2 ? `'${dataPoint.year}` : dataPoint.year;
          yearLabel.textContent = yearText;
        } else if (dataPoint.dateObj) {
          // Extract year from dateObj
          const yearText = `'${dataPoint.dateObj.getFullYear().toString().slice(-2)}`;
          yearLabel.textContent = yearText;
        } else {
          // Fallback to current year
          const currentYear = new Date().getFullYear().toString().slice(-2);
          yearLabel.textContent = `'${currentYear}`;
        }
        parent.appendChild(yearLabel);
      } else {
        // Show years for data spans longer than 18 months
        // Primary label shows year
        const yearLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        yearLabel.classList.add('snap-chart-column-label');
        yearLabel.setAttribute('x', centerX);
        yearLabel.setAttribute('y', chartHeight + 20);
        
        // Use actual year from data if available
        if (dataPoint.year) {
          // Use shortened year format with apostrophe (e.g., '16 instead of 2016)
          const shortYear = dataPoint.year.length === 2 ? `'${dataPoint.year}` : `'${dataPoint.year.slice(-2)}`;
          yearLabel.textContent = shortYear;
        } else if (dataPoint.dateObj) {
          // Extract year from dateObj and format with apostrophe
          const shortYear = `'${dataPoint.dateObj.getFullYear().toString().slice(-2)}`;
          yearLabel.textContent = shortYear;
        } else {
          // Fallback to current year with apostrophe
          const shortYear = `'${new Date().getFullYear().toString().slice(-2)}`;
          yearLabel.textContent = shortYear;
        }
        parent.appendChild(yearLabel);
        
        // Secondary label shows quarter info
        if (dataPoint.month) {
          const periodLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
          periodLabel.classList.add('snap-chart-column-label');
          periodLabel.setAttribute('x', centerX);
          periodLabel.setAttribute('y', chartHeight + 35);
          
          // Show quarter based on month
          const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
          const monthIndex = monthNames.indexOf(dataPoint.month);
          if (monthIndex !== -1) {
            const quarter = Math.floor(monthIndex / 3) + 1;
            periodLabel.textContent = `Q${quarter}`;
          } else {
            periodLabel.textContent = dataPoint.month.substring(0, 3); // First 3 characters
          }
          parent.appendChild(periodLabel);
        }
      }
    }
    
    // Only display sales value above column, royalties will be shown in tooltip
    if (dataPoint.sales && salesScale) {
      // Use actual column height if provided (for stacked columns with minimum height adjustments)
      // Otherwise fall back to calculated height (for other chart types)
      const totalColumnHeight = actualColumnHeight !== null ? actualColumnHeight : (dataPoint.sales * salesScale);
      
      // Calculate text position with proper spacing for Today vs Previous Years chart
      const valueHeight = this.options.isTodayVsPreviousYearsChart ? 16 : 14; // Larger height for bigger font
      const returnHeight = this.options.isTodayVsPreviousYearsChart ? 14 : 12; // Larger height for bigger font
      const textGap = this.options.isTodayVsPreviousYearsChart ? 6 : 2; // Increased gap for Today vs Previous Years
      const totalTextHeight = valueHeight + returnHeight + textGap;

      // Position text with 16px gap from column top
      const textStartY = chartHeight - totalColumnHeight - 16 - totalTextHeight;

      // Display sales value
      const valueLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      valueLabel.classList.add('snap-chart-column-value');
      // Apply reduced font size for Today vs Previous Years chart (25% reduction from 18px = 13.5px)
      if (this.options.isTodayVsPreviousYearsChart) {
        valueLabel.style.fontSize = '13.5px';
      }
      valueLabel.setAttribute('x', centerX);
      valueLabel.setAttribute('y', textStartY + valueHeight);
      valueLabel.textContent = dataPoint.sales.toLocaleString();
      parent.appendChild(valueLabel);

      // Return label (always shown) - positioned below value
      const returnValue = dataPoint.returns !== undefined ? dataPoint.returns : (dataPoint.change !== undefined ? dataPoint.change : 0);
      const returnLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      returnLabel.classList.add('snap-chart-column-change');
      // Apply reduced font size for Today vs Previous Years chart (25% reduction from 15px = 11.25px)
      if (this.options.isTodayVsPreviousYearsChart) {
        returnLabel.style.fontSize = '11.25px';
      }
      returnLabel.setAttribute('x', centerX);
      returnLabel.setAttribute('y', textStartY + valueHeight + textGap + returnHeight);
      // Format return value - always show in parentheses
      if (returnValue === 0) {
        returnLabel.textContent = '(0)';
        // Add zero-returns class for CSS styling
        returnLabel.classList.add('zero-returns');
      } else {
        const absReturnValue = Math.abs(returnValue);
        returnLabel.textContent = `(-${absReturnValue})`;
      }
      parent.appendChild(returnLabel);
    }
  }
  
  /**
   * Draw comparison column labels with 40% opacity - chart type specific
   */
  drawComparisonColumnLabels(parent, dataPoint, x, width, chartHeight, salesScale) {
    const centerX = x + width / 2;
    
    // Chart-type specific labeling for comparison columns
    if (this.type === 'stacked-column' || this.type === 'scrollable-stacked-column') {
      // Stacked columns: DO NOT show date labels for comparison columns
      // Only show sales values above columns (handled below)
    } else {
      // Daily sales history and other charts: Use existing time-span based logic
      const showMonthLabels = this.shouldShowMonthLabels();
      
      // Add month/year labels for comparison columns (with opacity)
      if (showMonthLabels) {
        // Show months for data spans of 18 months or less
        if (dataPoint.month) {
          const monthLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
          monthLabel.classList.add('snap-chart-column-label');
          monthLabel.classList.add('snap-chart-comparison-text'); // Add comparison class for 40% opacity
          monthLabel.setAttribute('x', centerX);
          monthLabel.setAttribute('y', chartHeight + 20);
          monthLabel.textContent = dataPoint.month;
          parent.appendChild(monthLabel);
        }
        
        // Year label below month
        const yearLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        yearLabel.classList.add('snap-chart-column-label');
        yearLabel.classList.add('snap-chart-comparison-text'); // Add comparison class for 40% opacity
        yearLabel.setAttribute('x', centerX);
        yearLabel.setAttribute('y', chartHeight + 35);
        
        // Use actual year from data if available
        if (dataPoint.year) {
          const yearText = dataPoint.year.length === 2 ? `'${dataPoint.year}` : dataPoint.year;
          yearLabel.textContent = yearText;
        } else if (dataPoint.dateObj) {
          const yearText = `'${dataPoint.dateObj.getFullYear().toString().slice(-2)}`;
          yearLabel.textContent = yearText;
        } else {
          const currentYear = new Date().getFullYear().toString().slice(-2);
          yearLabel.textContent = `'${currentYear}`;
        }
        parent.appendChild(yearLabel);
      } else {
        // Show years for data spans longer than 18 months
        const yearLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        yearLabel.classList.add('snap-chart-column-label');
        yearLabel.classList.add('snap-chart-comparison-text'); // Add comparison class for 40% opacity
        yearLabel.setAttribute('x', centerX);
        yearLabel.setAttribute('y', chartHeight + 20);
        
        // Use actual year from data if available
        if (dataPoint.year) {
          // Use shortened year format with apostrophe (e.g., '16 instead of 2016)
          const shortYear = dataPoint.year.length === 2 ? `'${dataPoint.year}` : `'${dataPoint.year.slice(-2)}`;
          yearLabel.textContent = shortYear;
        } else if (dataPoint.dateObj) {
          // Extract year from dateObj and format with apostrophe
          const shortYear = `'${dataPoint.dateObj.getFullYear().toString().slice(-2)}`;
          yearLabel.textContent = shortYear;
        } else {
          // Fallback to current year with apostrophe
          const shortYear = `'${new Date().getFullYear().toString().slice(-2)}`;
          yearLabel.textContent = shortYear;
        }
        parent.appendChild(yearLabel);
        
        // Secondary label shows quarter info
        if (dataPoint.month) {
          const periodLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
          periodLabel.classList.add('snap-chart-column-label');
          periodLabel.classList.add('snap-chart-comparison-text'); // Add comparison class for 40% opacity
          periodLabel.setAttribute('x', centerX);
          periodLabel.setAttribute('y', chartHeight + 35);
          
          const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
          const monthIndex = monthNames.indexOf(dataPoint.month);
          if (monthIndex !== -1) {
            const quarter = Math.floor(monthIndex / 3) + 1;
            periodLabel.textContent = `Q${quarter}`;
          } else {
            periodLabel.textContent = dataPoint.month.substring(0, 3);
          }
          parent.appendChild(periodLabel);
        }
      }
    }
    
    // Only display sales value above column, royalties will be shown in tooltip
    if (dataPoint.sales && salesScale) {
      const totalColumnHeight = dataPoint.sales * salesScale;
      
      // Calculate text position with proper spacing for Today vs Previous Years chart
      const valueHeight = this.options.isTodayVsPreviousYearsChart ? 16 : 14; // Larger height for bigger font
      const returnHeight = this.options.isTodayVsPreviousYearsChart ? 14 : 12; // Larger height for bigger font
      const textGap = this.options.isTodayVsPreviousYearsChart ? 6 : 2; // Increased gap for Today vs Previous Years
      const totalTextHeight = valueHeight + returnHeight + textGap;

      // Position text with 16px gap from column top
      const textStartY = chartHeight - totalColumnHeight - 16 - totalTextHeight;

      // Display sales value with comparison styling
      const valueLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      valueLabel.classList.add('snap-chart-column-value');
      valueLabel.classList.add('snap-chart-comparison-text'); // Add comparison class for 40% opacity
      // Apply reduced font size for Today vs Previous Years chart (25% reduction from 18px = 13.5px)
      if (this.options.isTodayVsPreviousYearsChart) {
        valueLabel.style.fontSize = '13.5px';
      }
      valueLabel.setAttribute('x', centerX);
      valueLabel.setAttribute('y', textStartY + valueHeight);
      valueLabel.textContent = dataPoint.sales.toLocaleString();
      parent.appendChild(valueLabel);

      // Return label (always shown) - positioned below value
      const returnValue = dataPoint.returns !== undefined ? dataPoint.returns : (dataPoint.change !== undefined ? dataPoint.change : 0);
      const returnLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      returnLabel.classList.add('snap-chart-column-change');
      returnLabel.classList.add('snap-chart-comparison-text'); // Add comparison class for 40% opacity
      // Apply reduced font size for Today vs Previous Years chart (25% reduction from 15px = 11.25px)
      if (this.options.isTodayVsPreviousYearsChart) {
        returnLabel.style.fontSize = '11.25px';
      }
      returnLabel.setAttribute('x', centerX);
      returnLabel.setAttribute('y', textStartY + valueHeight + textGap + returnHeight);
      // Format return value - always show in parentheses
      if (returnValue === 0) {
        returnLabel.textContent = '(0)';
        // Add zero-returns class for CSS styling
        returnLabel.classList.add('zero-returns');
      } else {
        const absReturnValue = Math.abs(returnValue);
        returnLabel.textContent = `(-${absReturnValue})`;
      }
      parent.appendChild(returnLabel);
    }
  }
  
  /**
   * Draw month labels based on date range, positioning by time progression
   * Labels are positioned at the left edge of each month's range
   */
  drawMonthLabelsFromDateRange(parent, startX, chartWidth, chartHeight, startDate, endDate) {
    // Check if we should show daily labels (when 30 or fewer columns are visible)
    if (this.shouldShowDailyLabels()) {
      this.drawDailyLabels(parent, startX, chartWidth, chartHeight);
      return;
    }
    
    if (!this.monthBoundaries || this.monthBoundaries.length === 0) return;
    
    // Determine labeling strategy based on time span (three-tier system)
    const showMonthLabels = this.shouldShowMonthLabels();
    const showOnlyYearLabels = this.shouldShowOnlyYearLabels();
    
    // Calculate total time span for positioning
    const totalTimeSpan = endDate.getTime() - startDate.getTime();
    
    // Calculate available width for labels (accounting for padding)
    const FIXED_PADDING = 32; // Same padding used in column positioning
    const availableWidth = chartWidth - (2 * FIXED_PADDING); // Width available for content
    const labelAreaStartX = startX; // startX already includes the padding
    
    if (showMonthLabels) {
      // Show month/year labels for shorter time spans (18 months or less)
      this.monthBoundaries.forEach((boundary, index) => {
        const boundaryDate = boundary.dateObj;
        
        // Only show labels for months within the selected date range
        if (boundaryDate >= startDate && boundaryDate <= endDate) {
          // Calculate position based on time progression - position at LEFT edge of month
          const timeProgress = (boundaryDate.getTime() - startDate.getTime()) / totalTimeSpan;
          const labelX = labelAreaStartX + (timeProgress * availableWidth);
          
          // Month label - left-aligned (text-anchor: start)
          const monthLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
          monthLabel.classList.add('snap-chart-column-label');
          monthLabel.setAttribute('x', labelX);
          monthLabel.setAttribute('y', chartHeight + 20);
          monthLabel.setAttribute('text-anchor', 'start'); // Left-align text
          monthLabel.textContent = boundary.month;
          parent.appendChild(monthLabel);
          
          // Year label below month - also left-aligned
          const yearLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
          yearLabel.classList.add('snap-chart-column-label');
          yearLabel.setAttribute('x', labelX);
          yearLabel.setAttribute('y', chartHeight + 35);
          yearLabel.setAttribute('text-anchor', 'start'); // Left-align text
          yearLabel.textContent = `'${boundary.year}`;
          parent.appendChild(yearLabel);
        }
      });
    } else if (showOnlyYearLabels) {
      // Show year-only labels for very long time spans (8+ years)
      // Group boundaries by year and show only year labels (no quarters)
      const yearGroups = {};
      
      this.monthBoundaries.forEach((boundary) => {
        const boundaryDate = boundary.dateObj;
        if (boundaryDate >= startDate && boundaryDate <= endDate) {
          const fullYear = boundaryDate.getFullYear();
          const yearKey = fullYear.toString();
          
          if (!yearGroups[yearKey]) {
            yearGroups[yearKey] = {
              year: fullYear,
              firstBoundaryDate: boundaryDate,
              boundaries: []
            };
          } else {
            // Keep track of the earliest boundary date for this year
            if (boundaryDate < yearGroups[yearKey].firstBoundaryDate) {
              yearGroups[yearKey].firstBoundaryDate = boundaryDate;
            }
          }
          yearGroups[yearKey].boundaries.push(boundary);
        }
      });
      
      // Create year-only labels
      Object.keys(yearGroups).forEach(yearKey => {
        const yearGroup = yearGroups[yearKey];
        if (yearGroup.boundaries.length > 0) {
          const boundaryDate = yearGroup.firstBoundaryDate;
          
          // Calculate position based on time progression - position at LEFT edge of year
          const timeProgress = (boundaryDate.getTime() - startDate.getTime()) / totalTimeSpan;
          const labelX = labelAreaStartX + (timeProgress * availableWidth);
          
          // Year label only - left-aligned, positioned at primary label line
          const yearLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
          yearLabel.classList.add('snap-chart-column-label');
          yearLabel.setAttribute('x', labelX);
          yearLabel.setAttribute('y', chartHeight + 20);
          yearLabel.setAttribute('text-anchor', 'start'); // Left-align text
          // Use shortened year format with apostrophe (e.g., '16 instead of 2016)
          const shortYear = `'${yearGroup.year.toString().slice(-2)}`;
          yearLabel.textContent = shortYear;
          parent.appendChild(yearLabel);
        }
      });
    } else {
      // Show year/quarter labels for medium time spans (18 months to 8 years)
      // Group boundaries by quarter and show year + quarter labels
      const quarterGroups = {};
      
      this.monthBoundaries.forEach((boundary) => {
        const boundaryDate = boundary.dateObj;
        if (boundaryDate >= startDate && boundaryDate <= endDate) {
          const fullYear = boundaryDate.getFullYear();
          const quarter = Math.floor(boundaryDate.getMonth() / 3) + 1;
          const quarterKey = `${fullYear}-Q${quarter}`;
          
          if (!quarterGroups[quarterKey]) {
            quarterGroups[quarterKey] = {
              year: fullYear,
              quarter: quarter,
              firstBoundaryDate: boundaryDate,
              boundaries: []
            };
          } else {
            // Keep track of the earliest boundary date for this quarter
            if (boundaryDate < quarterGroups[quarterKey].firstBoundaryDate) {
              quarterGroups[quarterKey].firstBoundaryDate = boundaryDate;
            }
          }
          quarterGroups[quarterKey].boundaries.push(boundary);
        }
      });
      
      // Create quarter labels
      Object.keys(quarterGroups).forEach(quarterKey => {
        const quarterGroup = quarterGroups[quarterKey];
        if (quarterGroup.boundaries.length > 0) {
          const boundaryDate = quarterGroup.firstBoundaryDate;
          
          // Calculate position based on time progression - position at LEFT edge of quarter
          const timeProgress = (boundaryDate.getTime() - startDate.getTime()) / totalTimeSpan;
          const labelX = labelAreaStartX + (timeProgress * availableWidth);
          
          // Year label (primary) - left-aligned
          const yearLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
          yearLabel.classList.add('snap-chart-column-label');
          yearLabel.setAttribute('x', labelX);
          yearLabel.setAttribute('y', chartHeight + 20);
          yearLabel.setAttribute('text-anchor', 'start'); // Left-align text
          // Use shortened year format with apostrophe (e.g., '16 instead of 2016)
          const shortYear = `'${quarterGroup.year.toString().slice(-2)}`;
          yearLabel.textContent = shortYear;
          parent.appendChild(yearLabel);
          
          // Quarter label (secondary) - left-aligned
          const quarterLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
          quarterLabel.classList.add('snap-chart-column-label');
          quarterLabel.setAttribute('x', labelX);
          quarterLabel.setAttribute('y', chartHeight + 35);
          quarterLabel.setAttribute('text-anchor', 'start'); // Left-align text
          quarterLabel.textContent = `Q${quarterGroup.quarter}`;
          parent.appendChild(quarterLabel);
        }
      });
    }
  }
  
  /**
   * Draw daily labels for each data point when zoomed in (30 columns or less)
   * Shows horizontal format for 1-19 columns: "OCT, 21" and "'25"
   * Shows vertical format for 20-30 columns: "OCT," "21" "'25"
   */
  drawDailyLabels(parent, startX, chartWidth, chartHeight) {
    if (!this.data || this.data.length === 0) return;
    
    // Calculate column positioning (same logic as in renderDailySalesHistory)
    const N = this.data.length;
    const gridInset = 48;
    const gridWidth = chartWidth - (2 * gridInset);
    const FIXED_PADDING = 32;
    const columnAreaStartX = gridInset + FIXED_PADDING;
    const columnAreaEndX = gridInset + gridWidth - FIXED_PADDING;
    const columnAreaWidth = columnAreaEndX - columnAreaStartX;
    
    // Calculate column width and spacing (same logic as in renderDailySalesHistory)
    const minColumnWidth = 1;
    let columnWidth = Math.max(minColumnWidth, Math.floor(columnAreaWidth / (N * 1.5)));
    
    let gapBetweenColumns;
    if (N === 0) {
      columnWidth = minColumnWidth;
      gapBetweenColumns = 0;
    } else if (N === 1) {
      columnWidth = Math.min(columnAreaWidth, Math.max(24, columnAreaWidth / 4));
      gapBetweenColumns = 0;
    } else if (N === 2) {
      columnWidth = Math.max(minColumnWidth, Math.floor(columnAreaWidth / 10));
      gapBetweenColumns = columnAreaWidth - (2 * columnWidth);
    } else {
      const totalSpacingWidth = columnAreaWidth - (N * columnWidth);
      gapBetweenColumns = totalSpacingWidth / (N - 1);
    }
    
    const columnStartX = columnAreaStartX;
    
    // Determine label format based on number of columns
    const useVerticalLabels = N >= 20; // Use vertical labels for 20-30 columns, horizontal for 1-19
    
    // Draw daily labels for each data point
    for (let i = 0; i < N; i++) {
      const dataPoint = this.data[i];
      if (!dataPoint.dateObj) continue;
      
      // Calculate column center position
      const columnX = columnStartX + (i * (columnWidth + gapBetweenColumns));
      const centerX = columnX + columnWidth / 2;
      
      // Format date for daily labels
      const date = dataPoint.dateObj;
      const monthName = date.toLocaleDateString('en-US', { month: 'short' }).toUpperCase();
      const dayNumber = date.getDate();
      const year = date.getFullYear().toString().slice(-2);
      
      if (useVerticalLabels) {
        // Vertical format for 20-30 columns (space-saving)
        // First line: "OCT," (month abbreviation + comma)
        const monthLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        monthLabel.classList.add('snap-chart-column-label');
        monthLabel.setAttribute('x', centerX);
        monthLabel.setAttribute('y', chartHeight + 20);
        monthLabel.setAttribute('text-anchor', 'middle');
        monthLabel.textContent = `${monthName},`;
        parent.appendChild(monthLabel);
        
        // Second line: "21" (day number)
        const dayLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        dayLabel.classList.add('snap-chart-column-label');
        dayLabel.setAttribute('x', centerX);
        dayLabel.setAttribute('y', chartHeight + 32);
        dayLabel.setAttribute('text-anchor', 'middle');
        dayLabel.textContent = `${dayNumber}`;
        parent.appendChild(dayLabel);
        
        // Third line: "'25" (year with apostrophe)
        const yearLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        yearLabel.classList.add('snap-chart-column-label');
        yearLabel.setAttribute('x', centerX);
        yearLabel.setAttribute('y', chartHeight + 44);
        yearLabel.setAttribute('text-anchor', 'middle');
        yearLabel.textContent = `'${year}`;
        parent.appendChild(yearLabel);
      } else {
        // Horizontal format for 1-19 columns (original format)
        // First line: "OCT, 21" (month abbreviation + comma + day number)
        const dayLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        dayLabel.classList.add('snap-chart-column-label');
        dayLabel.setAttribute('x', centerX);
        dayLabel.setAttribute('y', chartHeight + 20);
        dayLabel.setAttribute('text-anchor', 'middle');
        dayLabel.textContent = `${monthName}, ${dayNumber}`;
        parent.appendChild(dayLabel);
        
        // Second line: "'25" (year with apostrophe)
        const yearLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        yearLabel.classList.add('snap-chart-column-label');
        yearLabel.setAttribute('x', centerX);
        yearLabel.setAttribute('y', chartHeight + 35);
        yearLabel.setAttribute('text-anchor', 'middle');
        yearLabel.textContent = `'${year}`;
        parent.appendChild(yearLabel);
      }
    }
  }
  
  /**
   * Draw month labels using month boundaries for clean labeling (legacy method - still used for data-based positioning)
   */
  drawMonthLabelsFromBoundaries(parent, startX, columnWidth, gapBetweenColumns, chartHeight) {
    if (!this.monthBoundaries || this.monthBoundaries.length === 0) return;
    
    // Calculate the date range covered by the actual data
    const dataStartDate = this.data.length > 0 ? this.data[0].dateObj : null;
    const dataEndDate = this.data.length > 0 ? this.data[this.data.length - 1].dateObj : null;
    
    if (!dataStartDate || !dataEndDate) return;
    
    // Determine labeling strategy based on time span
    const showMonthLabels = this.shouldShowMonthLabels();
    
    // Calculate total data range in days for positioning
    const totalDataDays = Math.ceil((dataEndDate - dataStartDate) / (1000 * 60 * 60 * 24)) + 1;
    const totalDataWidth = this.data.length * (columnWidth + gapBetweenColumns) - gapBetweenColumns;
    
    if (showMonthLabels) {
      // Show month/year labels for shorter time spans (18 months or less)
      this.monthBoundaries.forEach((boundary, index) => {
        // Calculate where this month boundary should appear relative to the data
        const boundaryDate = boundary.dateObj;
        
        // Only show labels for months that have data or are within the visible range
        if (boundaryDate >= dataStartDate && boundaryDate <= dataEndDate) {
          // Find the position based on the first data point of this month
          let monthDataStartIndex = -1;
          for (let i = 0; i < this.data.length; i++) {
            const dataDate = this.data[i].dateObj;
            if (dataDate.getMonth() === boundaryDate.getMonth() && 
                dataDate.getFullYear() === boundaryDate.getFullYear()) {
              monthDataStartIndex = i;
              break;
            }
          }
          
          if (monthDataStartIndex >= 0) {
            // Position the label at the first data point of this month
            const labelX = startX + (monthDataStartIndex * (columnWidth + gapBetweenColumns)) + (columnWidth / 2);
            
            // Month label
            const monthLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            monthLabel.classList.add('snap-chart-column-label');
            monthLabel.setAttribute('x', labelX);
            monthLabel.setAttribute('y', chartHeight + 20);
            monthLabel.textContent = boundary.month;
            parent.appendChild(monthLabel);
            
            // Year label below month
            const yearLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            yearLabel.classList.add('snap-chart-column-label');
            yearLabel.setAttribute('x', labelX);
            yearLabel.setAttribute('y', chartHeight + 35);
            yearLabel.textContent = `'${boundary.year}`;
            parent.appendChild(yearLabel);
          }
        }
      });
    } else {
      // Show year labels for longer time spans (more than 18 months)
      // Group boundaries by quarter and show year + quarter labels
      const quarterGroups = {};
      
      this.monthBoundaries.forEach((boundary) => {
        const boundaryDate = boundary.dateObj;
        if (boundaryDate >= dataStartDate && boundaryDate <= dataEndDate) {
          const fullYear = boundaryDate.getFullYear();
          const quarter = Math.floor(boundaryDate.getMonth() / 3) + 1;
          const quarterKey = `${fullYear}-Q${quarter}`;
          
          if (!quarterGroups[quarterKey]) {
            quarterGroups[quarterKey] = {
              year: fullYear,
              quarter: quarter,
              boundaries: []
            };
          }
          quarterGroups[quarterKey].boundaries.push(boundary);
        }
      });
      
      // Create quarter labels
      Object.keys(quarterGroups).forEach(quarterKey => {
        const quarterGroup = quarterGroups[quarterKey];
        if (quarterGroup.boundaries.length > 0) {
          // Use the first boundary of the quarter for positioning
          const firstBoundary = quarterGroup.boundaries[0];
          const boundaryDate = firstBoundary.dateObj;
          
          // Find the position based on the first data point of this quarter
          let quarterDataStartIndex = -1;
          for (let i = 0; i < this.data.length; i++) {
            const dataDate = this.data[i].dateObj;
            const dataQuarter = Math.floor(dataDate.getMonth() / 3) + 1;
            if (dataDate.getFullYear() === boundaryDate.getFullYear() && 
                dataQuarter === quarterGroup.quarter) {
              quarterDataStartIndex = i;
              break;
            }
          }
          
          if (quarterDataStartIndex >= 0) {
            // Position the label at the first data point of this quarter
            const labelX = startX + (quarterDataStartIndex * (columnWidth + gapBetweenColumns)) + (columnWidth / 2);
            
                      // Year label (primary)
          const yearLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
          yearLabel.classList.add('snap-chart-column-label');
          yearLabel.setAttribute('x', labelX);
          yearLabel.setAttribute('y', chartHeight + 20);
          // Use shortened year format with apostrophe (e.g., '16 instead of 2016)
          const shortYear = `'${quarterGroup.year.toString().slice(-2)}`;
          yearLabel.textContent = shortYear;
          parent.appendChild(yearLabel);
            
            // Quarter label (secondary)
            const quarterLabel = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            quarterLabel.classList.add('snap-chart-column-label');
            quarterLabel.setAttribute('x', labelX);
            quarterLabel.setAttribute('y', chartHeight + 35);
            quarterLabel.textContent = `Q${quarterGroup.quarter}`;
            parent.appendChild(quarterLabel);
          }
        }
      });
    }
  }
  
  /**
   * Draw selection indicator (disabled)
   */
  drawSelectionIndicator(parent, columnIndex, columnWidth, chartHeight) {
    // Selection indicator disabled as requested
    return;
  }
  
  /**
   * Create dual-handle date range slider for daily sales history
   */
  createDateRangeControls(parent, chartWidth, yPosition) {
    // Get all-time data and current range from options
    const allTimeData = this.options.allTimeData || this.data;
    let currentStartDate = this.options.currentStartDate;
    let currentEndDate = this.options.currentEndDate;
    
    // Handle empty data case - create slider with default date range
    if (!allTimeData || allTimeData.length === 0) {
      console.warn('No all-time data available for date range slider, using default dates');
      // Set default date range for empty data - use current options or last 30 days
      const today = new Date();
      const thirtyDaysAgo = new Date(today);
      thirtyDaysAgo.setDate(today.getDate() - 30);
      
      currentStartDate = currentStartDate || thirtyDaysAgo;
      currentEndDate = currentEndDate || today;
    }
    
    // Create date range control group
    const dateRangeGroup = document.createElementNS('http://www.w3.org/2000/svg', 'g');
    dateRangeGroup.classList.add('snap-chart-date-range-controls');
    dateRangeGroup.setAttribute('transform', `translate(0, ${yPosition})`);
    
    // No background - clean slider-only design
    
    // Slider dimensions - track represents actual data timeline
    const sliderMargin = 40; // Increased margin to provide space for handles at edges
    const sliderWidth = chartWidth - (2 * sliderMargin);
    const sliderY = 25; // Moved down from 15 to 25 to center in expanded background
    const sliderHeight = 24; // Increased to 24px to match new Figma design
    
    // Track represents the ACTUAL data timeline - left edge = first data, right edge = last data
    const sliderTrack = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    sliderTrack.classList.add('snap-chart-date-slider-track');
    sliderTrack.setAttribute('x', sliderMargin);
    sliderTrack.setAttribute('y', sliderY);
    sliderTrack.setAttribute('width', sliderWidth);
    sliderTrack.setAttribute('height', sliderHeight);
    sliderTrack.setAttribute('rx', '12');
    sliderTrack.setAttribute('ry', '12');
    dateRangeGroup.appendChild(sliderTrack);
    
    // Calculate positions for current date range - ALIGNED WITH YEAR MARKS
    let allTimeStart, allTimeEnd;
    
    if (allTimeData && allTimeData.length > 0) {
      allTimeStart = new Date(allTimeData[0].dateObj);
      // Use today's date as the end point instead of last data date to show potential data gaps
      const today = new Date();
      const lastDataDate = new Date(allTimeData[allTimeData.length - 1].dateObj);
      allTimeEnd = new Date(Math.max(today.getTime(), lastDataDate.getTime())); // Use whichever is later
    } else {
      // For empty data, use a reasonable default range
      const today = new Date();
      const oneYearAgo = new Date(today);
      oneYearAgo.setFullYear(today.getFullYear() - 1);
      allTimeStart = oneYearAgo;
      allTimeEnd = today;
    }
    
    const totalDays = Math.ceil((allTimeEnd - allTimeStart) / (1000 * 60 * 60 * 24)); // Keep for controls functionality
    
    // SIMPLE PROPORTIONAL POSITIONING - track represents data timeline
    const totalDataTimeSpan = allTimeEnd.getTime() - allTimeStart.getTime();
    
    // Calculate proportional positions within the data timeline
    const startRatio = (currentStartDate.getTime() - allTimeStart.getTime()) / totalDataTimeSpan;
    const endRatio = (currentEndDate.getTime() - allTimeStart.getTime()) / totalDataTimeSpan;
    
    // Map ratios directly to track positions (NO artificial constraints)
    const trackStart = sliderMargin;
    const trackEnd = sliderMargin + sliderWidth;
    
    let startPosition = trackStart + (startRatio * sliderWidth);
    let endPosition = trackStart + (endRatio * sliderWidth);
    
    // Ensure minimum range width of 50px (represents 7 days minimum)
    const minRangeWidth = 50;
    let rangeWidth = endPosition - startPosition;
    
    if (rangeWidth < minRangeWidth) {
      // If range is too small, expand it to represent exactly 7 days
      const currentTimeSpan = currentEndDate.getTime() - currentStartDate.getTime();
      const minimumTimeSpan = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
      
      // If current range is less than 7 days, expand to 7 days
      if (currentTimeSpan < minimumTimeSpan) {
        const center = (currentStartDate.getTime() + currentEndDate.getTime()) / 2;
        const newStartDate = new Date(center - (minimumTimeSpan / 2));
        const newEndDate = new Date(center + (minimumTimeSpan / 2));
        
        // Recalculate positions based on 7 day range
        const newStartRatio = (newStartDate.getTime() - allTimeStart.getTime()) / totalDataTimeSpan;
        const newEndRatio = (newEndDate.getTime() - allTimeStart.getTime()) / totalDataTimeSpan;
        
        startPosition = trackStart + (newStartRatio * sliderWidth);
        endPosition = trackStart + (newEndRatio * sliderWidth);
      } else {
        // Visual minimum: expand range while keeping it centered
        const center = (startPosition + endPosition) / 2;
        startPosition = center - (minRangeWidth / 2);
        endPosition = center + (minRangeWidth / 2);
      }
      
      // If expanded range goes outside track bounds, adjust
      if (startPosition < trackStart) {
        startPosition = trackStart;
        endPosition = startPosition + minRangeWidth;
      } else if (endPosition > trackEnd) {
        endPosition = trackEnd;
        startPosition = endPosition - minRangeWidth;
      }
      
      rangeWidth = endPosition - startPosition;
    }
    
    // Prevent invalid positions (start >= end) - should not happen with minimum width logic
    if (startPosition >= endPosition) {
      endPosition = Math.min(startPosition + minRangeWidth, trackEnd);
      rangeWidth = endPosition - startPosition;
    }
    
    // Selected date range (colored section) - constrained within track
    const sliderRange = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    sliderRange.classList.add('snap-chart-date-slider-range');
    sliderRange.setAttribute('x', startPosition);
    sliderRange.setAttribute('y', sliderY);
    sliderRange.setAttribute('width', rangeWidth);
    sliderRange.setAttribute('height', sliderHeight);
    sliderRange.setAttribute('rx', '12');
    sliderRange.setAttribute('ry', '12');
    dateRangeGroup.appendChild(sliderRange);
    
    // Left handle (start date) - Figma style white circle positioned at left edge of range
    const leftHandle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
    leftHandle.classList.add('snap-chart-date-slider-handle', 'left-handle');
    leftHandle.setAttribute('cx', startPosition + 6 + 6); // 6px inset from left edge + 6px radius = handle edge 6px from range edge
    leftHandle.setAttribute('cy', sliderY + (sliderHeight / 2)); // Center vertically in 24px range
    leftHandle.setAttribute('r', '6'); // 6px radius = 12px diameter as specified
    dateRangeGroup.appendChild(leftHandle);
    
    // Right handle (end date) - Figma style white circle positioned at right edge of range
    const rightHandle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
    rightHandle.classList.add('snap-chart-date-slider-handle', 'right-handle');
    rightHandle.setAttribute('cx', endPosition - 6 - 6); // 6px inset from right edge - 6px radius = handle edge 6px from range edge
    rightHandle.setAttribute('cy', sliderY + (sliderHeight / 2)); // Center vertically in 24px range
    rightHandle.setAttribute('r', '6'); // 6px radius = 12px diameter as specified
    dateRangeGroup.appendChild(rightHandle);
    

    
    // Add drag indicator in center of selected range - White design
    const centerX = startPosition + (rangeWidth / 2);
    const dragIndicator1 = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    dragIndicator1.classList.add('snap-chart-date-slider-drag-indicator');
    dragIndicator1.setAttribute('x', centerX - 3); // Position first bar 3px left of center
    dragIndicator1.setAttribute('y', sliderY + (sliderHeight / 2) - 4); // Center vertically, 4px above center
    dragIndicator1.setAttribute('width', '2'); // 2px wide as per Figma
    dragIndicator1.setAttribute('height', '8'); // 8px tall as per Figma
    dragIndicator1.setAttribute('rx', '1'); // 1px border radius
    dragIndicator1.setAttribute('ry', '1'); // 1px border radius
    dateRangeGroup.appendChild(dragIndicator1);
    
    const dragIndicator2 = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    dragIndicator2.classList.add('snap-chart-date-slider-drag-indicator');
    dragIndicator2.setAttribute('x', centerX + 1); // Position second bar 1px right of center
    dragIndicator2.setAttribute('y', sliderY + (sliderHeight / 2) - 4); // Center vertically, 4px above center
    dragIndicator2.setAttribute('width', '2'); // 2px wide as per Figma
    dragIndicator2.setAttribute('height', '8'); // 8px tall as per Figma
    dragIndicator2.setAttribute('rx', '1'); // 1px border radius
    dragIndicator2.setAttribute('ry', '1'); // 1px border radius
    dateRangeGroup.appendChild(dragIndicator2);
    
    // Add scale marks below slider - Figma style
    this.createSliderScaleMarks(dateRangeGroup, sliderMargin, sliderWidth, sliderY, sliderHeight);
    
    // Year scale labels below scale marks (based on available data years) - with click functionality
    // Add these BEFORE date range value backgrounds so they appear underneath
    this.createYearScaleLabels(dateRangeGroup, sliderMargin, sliderWidth, sliderY + sliderHeight + 1 + 13 + 5, allTimeStart, allTimeEnd);
    
    // Position text labels with proper spacing to avoid overlap with slider
    // Start date: positioned to the left with adequate spacing, End date: positioned to the right with adequate spacing  
    const leftTextX = startPosition - 16; // 16px to the left of range start to avoid overlap
    const rightTextX = endPosition + 16; // 16px to the right of range end to avoid overlap
    
    // Position text with 1px top margin (move down by 1px)
    const textY = sliderY + (sliderHeight / 2) + 1; // Center vertically with slider, then move down 1px
    
    // No background elements - just text labels
    
    // Date labels positioned with adequate spacing to prevent overlap
    const leftValueText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    leftValueText.classList.add('snap-chart-date-range-value-label');
    leftValueText.setAttribute('x', leftTextX);
    leftValueText.setAttribute('y', textY);
    leftValueText.setAttribute('text-anchor', 'end'); // Right-align text to position it properly to the left of range
    leftValueText.textContent = this.formatCompactDate(currentStartDate);
    dateRangeGroup.appendChild(leftValueText);
    
    const rightValueText = document.createElementNS('http://www.w3.org/2000/svg', 'text');
    rightValueText.classList.add('snap-chart-date-range-value-label');
    rightValueText.setAttribute('x', rightTextX);
    rightValueText.setAttribute('y', textY);
    rightValueText.setAttribute('text-anchor', 'start'); // Left-align text to position it properly to the right of range
    rightValueText.textContent = this.formatCompactDate(currentEndDate);
    dateRangeGroup.appendChild(rightValueText);
    
    parent.appendChild(dateRangeGroup);
    
    // Store references for drag functionality
    this.dateRangeControls = {
      group: dateRangeGroup,
      track: sliderTrack,
      range: sliderRange,
      leftHandle: leftHandle,
      rightHandle: rightHandle,
      dragIndicator1: dragIndicator1,
      dragIndicator2: dragIndicator2,

      leftValueText: leftValueText,
      rightValueText: rightValueText,
      sliderMargin: sliderMargin,
      sliderWidth: sliderWidth,
      sliderY: sliderY,
      sliderHeight: sliderHeight,
      allTimeStart: allTimeStart,
      allTimeEnd: allTimeEnd,
      totalDays: totalDays
    };
    
    // Set up drag functionality
    this.setupDateRangeSliderEvents();
  }
  
  /**
   * Create scale marks below the slider - Dynamic based on year range
   */
  createSliderScaleMarks(parent, sliderMargin, sliderWidth, sliderY, sliderHeight) {
    // Get the year range from the dateRangeControls
    if (!this.dateRangeControls || !this.dateRangeControls.allTimeStart || !this.dateRangeControls.allTimeEnd) {
      console.warn('Missing date range controls or time boundaries');
      return;
    }
    
    const startYear = this.dateRangeControls.allTimeStart.getFullYear();
    const endYear = this.dateRangeControls.allTimeEnd.getFullYear();
    const yearSpan = endYear - startYear + 1;
    
    // Determine how many marks to show based on the span
    let yearStep = 1;
    if (yearSpan > 10) {
      yearStep = 2; // Every 2 years for spans > 10 years
    } else if (yearSpan > 5) {
      yearStep = 1; // Every year for spans 6-10 years
    }
    
    // Generate array of years for marks - only years that have labels
    const years = [];
    for (let year = startYear; year <= endYear; year += yearStep) {
      years.push(year);
    }
    // If the last year is not included due to step, add it only if it matches the edge
    if (years[years.length - 1] !== endYear && (this.dateRangeControls.allTimeEnd.getMonth() === 11 && this.dateRangeControls.allTimeEnd.getDate() === 31)) {
      years.push(endYear);
    }
    
    // Use same spacing as year labels to ensure perfect alignment
    const markSpacing = yearSpan > 1 ? sliderWidth / yearSpan : 0;
    
    // Scale marks removed - keeping the rest of the function logic intact
    // The years array and positioning calculations are still available for other uses
    // but we no longer create the visual scale mark elements
  }
  
  /**
   * Format date for display in slider labels
   */
  formatDateLabel(date) {
    const month = date.toLocaleDateString('en-US', { month: 'short' }).toUpperCase();
    const day = date.getDate().toString().padStart(2, '0');
    const year = date.getFullYear().toString(); // Full 4-digit year
    return `${month} ${day}, ${year}`;
  }

  /**
   * Format date for compact display above handles (e.g., "DEC 03, '25")
   */
  formatCompactDate(date) {
    const month = date.toLocaleDateString('en-US', { month: 'short' }).toUpperCase();
    const day = date.getDate().toString().padStart(2, '0'); // Pad day with leading zero
    const year = date.getFullYear().toString().slice(-2); // Last 2 digits of year
    return `${month} ${day}, '${year}`;
  }

    /**
   * Create year scale labels below the scale marks - Dynamically based on data range
   */
  createYearScaleLabels(parent, startX, width, yPosition, allTimeStart, allTimeEnd) {
    // Calculate the year range from the actual data
    const startYear = allTimeStart.getFullYear();
    const endYear = allTimeEnd.getFullYear();
    const yearSpan = endYear - startYear + 1;
    
    // Determine how many year labels to show based on the span
    let yearStep = 1;
    if (yearSpan > 10) {
      yearStep = 2; // Every 2 years for spans > 10 years
    } else if (yearSpan > 5) {
      yearStep = 1; // Every year for spans 6-10 years
    }
    
    // Generate array of years to display (do NOT add extra year at the end)
    const years = [];
    for (let year = startYear; year <= endYear; year += yearStep) {
      years.push(year);
    }
    // If the last year is not included due to step, add it only if it matches the edge
    if (years[years.length - 1] !== endYear && (allTimeEnd.getMonth() === 11 && allTimeEnd.getDate() === 31)) {
      years.push(endYear);
    }
    
    // Position years proportionally within the actual data timeline
    const totalDataTimeSpan = allTimeEnd.getTime() - allTimeStart.getTime();
    
    // Position years at the START of each year's segment
    years.forEach((year, index) => {
      // Calculate year start position within the data timeline
      const yearStart = new Date(year, 0, 1);
      const yearRatio = (yearStart.getTime() - allTimeStart.getTime()) / totalDataTimeSpan;
      let markX = startX + (yearRatio * width);
      
      // Center text exactly under the scale mark
      const textX = markX;
      const label = document.createElementNS('http://www.w3.org/2000/svg', 'text');
      label.classList.add('snap-chart-date-range-scale-label', 'clickable-year');
      label.setAttribute('x', textX);
      label.setAttribute('y', yPosition);
      label.setAttribute('text-anchor', 'middle');
      label.setAttribute('data-year', year);
      // Use shortened year format with apostrophe (e.g., '16 instead of 2016)
      const shortYear = `'${year.toString().slice(-2)}`;
      label.textContent = shortYear;
      label.style.cursor = 'pointer';
      // Add click event to filter to this year
      label.addEventListener('click', (e) => {
        e.stopPropagation();
        this.filterToYear(year);
      });
      parent.appendChild(label);
    });
  }

  /**
   * Filter chart to show only the selected timeframe (month, quarter, or year)
   * Only available for daily-sales-history charts
   */
  filterToTimeframe(group) {
    // Only allow filtering for daily-sales-history charts
    if (this.type !== 'daily-sales-history') {
      console.warn('SnapChart: Timeframe filtering is only available for daily-sales-history charts');
      return;
    }
    
    if (!group || !group.dataPoints || group.dataPoints.length === 0) {
      console.warn('SnapChart: Invalid group for timeframe filtering');
      return;
    }
    
    // Calculate the period boundaries based on group type
    const periodBoundaries = this.calculatePeriodBoundaries(group);
    if (!periodBoundaries) {
      console.warn('SnapChart: Could not calculate period boundaries');
      return;
    }
    
    const { startDate, endDate, periodLabel } = periodBoundaries;
    
    // Set period tracking for label-based filtering
    this.currentPeriodType = 'label';
    this.currentPeriodId = group.type;
    this.currentPeriodDuration = Math.ceil((endDate.getTime() - startDate.getTime()) / (24 * 60 * 60 * 1000));
    
    // Set label-initiated flag to bypass minimum width enforcement for label clicks
    this.isLabelInitiatedFilter = true;
    
    // For daily sales history, use existing date range functionality
    this.filterDailySalesHistoryToTimeframe(startDate, endDate, periodLabel);
    
    // Reset label-initiated flag after filtering
    this.isLabelInitiatedFilter = false;
    
    // Trigger custom event for timeframe selection
    this.containerElement.dispatchEvent(new CustomEvent('timeframeSelect', {
      detail: { 
        type: group.type, 
        label: group.label, 
        sublabel: group.sublabel,
        startDate, 
        endDate,
        periodLabel
      }
    }));
  }
  
  /**
   * Calculate period boundaries for a timeframe group
   */
  calculatePeriodBoundaries(group) {
    if (!group.dataPoints || group.dataPoints.length === 0) return null;
    
    const firstDataPoint = group.dataPoints[0];
    let startDate, endDate, periodLabel;
    
    if (group.type === 'month') {
      // Month: from 1st to last day of the month
      if (firstDataPoint.dateObj) {
        const year = firstDataPoint.dateObj.getFullYear();
        const month = firstDataPoint.dateObj.getMonth();
        startDate = new Date(year, month, 1);
        endDate = new Date(year, month + 1, 1);
        endDate.setMilliseconds(endDate.getMilliseconds() - 1);
        periodLabel = `${group.label} ${year}`;
      } else {
        // For non-daily data, use the group's month/year info
        const monthNames = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];
        const monthIndex = monthNames.indexOf(group.label);
        const year = parseInt('20' + group.sublabel.replace("'", ""));
        if (monthIndex !== -1) {
          startDate = new Date(year, monthIndex, 1);
          endDate = new Date(year, monthIndex + 1, 1);
          endDate.setMilliseconds(endDate.getMilliseconds() - 1);
          periodLabel = `${group.label} ${year}`;
        }
      }
    } else if (group.type === 'quarter') {
      // Quarter: from 1st day of first month to last day of last month
      if (firstDataPoint.dateObj) {
        const year = firstDataPoint.dateObj.getFullYear();
        const month = firstDataPoint.dateObj.getMonth();
        const quarterStartMonth = Math.floor(month / 3) * 3;
        startDate = new Date(year, quarterStartMonth, 1);
        endDate = new Date(year, quarterStartMonth + 3, 1);
        endDate.setMilliseconds(endDate.getMilliseconds() - 1);
        const quarter = Math.floor(month / 3) + 1;
        periodLabel = `Q${quarter} ${year}`;
      } else {
        // Extract quarter info from sublabel (e.g., "Q1")
        const quarterMatch = group.sublabel.match(/Q(\d)/);
        const year = parseInt('20' + group.label.replace("'", ""));
        if (quarterMatch) {
          const quarter = parseInt(quarterMatch[1]);
          const quarterStartMonth = (quarter - 1) * 3;
          startDate = new Date(year, quarterStartMonth, 1);
          endDate = new Date(year, quarterStartMonth + 3, 1);
          endDate.setMilliseconds(endDate.getMilliseconds() - 1);
          periodLabel = `Q${quarter} ${year}`;
        }
      }
    } else if (group.type === 'year') {
      // Year: from Jan 1 to Dec 31
      if (firstDataPoint.dateObj) {
        const year = firstDataPoint.dateObj.getFullYear();
        startDate = new Date(year, 0, 1);
        endDate = new Date(year + 1, 0, 1);
        endDate.setMilliseconds(endDate.getMilliseconds() - 1);
        periodLabel = year.toString();
      } else {
        const year = parseInt('20' + group.label.replace("'", ""));
        startDate = new Date(year, 0, 1);
        endDate = new Date(year + 1, 0, 1);
        endDate.setMilliseconds(endDate.getMilliseconds() - 1);
        periodLabel = year.toString();
      }
    }
    
    return startDate && endDate ? { startDate, endDate, periodLabel } : null;
  }
  
  /**
   * Filter daily sales history to show specific timeframe
   */
  filterDailySalesHistoryToTimeframe(startDate, endDate, periodLabel) {
    if (!this.options.allTimeData) {
      console.warn('No all-time data available for timeframe filtering');
      return;
    }
    
    // Calculate current date information for month detection
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();
    
    // Determine if the filtered period represents the current month
    const isCurrentMonth = (
      startDate.getFullYear() === currentYear &&
      startDate.getMonth() === currentMonth &&
      endDate.getFullYear() === currentYear &&
      endDate.getMonth() === currentMonth
    );
    
    // Find the last available month in the data
    let lastAvailableMonth = null;
    let lastAvailableYear = null;
    if (this.options.allTimeData && this.options.allTimeData.length > 0) {
      const lastDataPoint = this.options.allTimeData[this.options.allTimeData.length - 1];
      if (lastDataPoint && lastDataPoint.dateObj) {
        lastAvailableMonth = lastDataPoint.dateObj.getMonth();
        lastAvailableYear = lastDataPoint.dateObj.getFullYear();
      }
    }
    
    // Determine if the filtered period represents the last available month
    const isLastAvailableMonth = (
      lastAvailableMonth !== null &&
      lastAvailableYear !== null &&
      startDate.getFullYear() === lastAvailableYear &&
      startDate.getMonth() === lastAvailableMonth &&
      endDate.getFullYear() === lastAvailableYear &&
      endDate.getMonth() === lastAvailableMonth
    );
    
    // Calculate time spans for minimum width enforcement
    const dayInMs = 24 * 60 * 60 * 1000;
    const minimumDays = 7; // Minimum 7 days (7 columns)
    const minimumTimeSpan = minimumDays * dayInMs; // 7 days in milliseconds
    
    const currentTimeSpan = endDate.getTime() - startDate.getTime();
    const currentDays = Math.floor(currentTimeSpan / dayInMs) + 1;
    
    let adjustedStartDate = startDate;
    let adjustedEndDate = endDate;
    
    // MANDATORY: Always enforce minimum 50px slider width (7 days minimum)
    // This rule must NEVER be violated under any circumstances
    // EXCEPT when filtering is initiated by tabs or labels (bypass for tab/label-based filtering)
    // OR when we have limited data that makes 7-day minimum meaningless
    const limitedDataInfo = this.hasLimitedData();
    const shouldBypassMinimumWidth = this.isTabInitiatedFilter || this.isLabelInitiatedFilter || limitedDataInfo.isLimited;
    
    if (currentDays < minimumDays && !shouldBypassMinimumWidth) {
      // Expand to minimum 7 days centered on the current selection
      const center = (startDate.getTime() + endDate.getTime()) / 2;
      adjustedStartDate = new Date(center - (minimumTimeSpan / 2));
      adjustedEndDate = new Date(center + (minimumTimeSpan / 2));
      
      // Final boundary check to ensure we don't go outside available data
      if (this.dateRangeControls) {
        const controls = this.dateRangeControls;
        if (adjustedStartDate < controls.allTimeStart) {
          adjustedStartDate = new Date(controls.allTimeStart);
          adjustedEndDate = new Date(adjustedStartDate.getTime() + minimumTimeSpan);
        } else if (adjustedEndDate > controls.allTimeEnd) {
          adjustedEndDate = new Date(controls.allTimeEnd);
          adjustedStartDate = new Date(adjustedEndDate.getTime() - minimumTimeSpan);
        }
      }
      
      // ... existing code ...
    }
    
    // Filter data to the adjusted timeframe (which ensures minimum width)
    const filteredData = this.options.allTimeData.filter(d => 
      d.dateObj >= adjustedStartDate && d.dateObj <= adjustedEndDate
    );
    
    // Check if this is a label drill-down (any specific period clicked from labels)
    // This function is called when user clicks on month/quarter/year labels
    // We want to show only actual data for the selected period, no gap filling
    // For tab-initiated filtering, keep the flag as set by applyDateFilter()
    if (!this.isTabInitiatedFilter && this.isLabelInitiatedFilter) {
      this.isLabelDrillDown = true;
    }
    
    // Store the label-initiated flag for later use in checkSliderTabMatch
    this.wasLabelInitiated = this.isLabelInitiatedFilter;
    
    // For label drill-downs, show actual data with gap filling for current/last month
    // For manual date range selections, show complete timeframe with gaps filled
    let dataWithGaps;
    if (this.isLabelDrillDown) {
      if (isCurrentMonth || isLastAvailableMonth) {
        // For current/last month, fill gaps to show complete month structure
        dataWithGaps = this.addZeroSalesMonthsToToday(filteredData, adjustedEndDate);
      } else {
        // For other months, show only actual data
        dataWithGaps = filteredData;
      }
    } else {
      // Manual date range selections always show complete timeframe with gaps
      dataWithGaps = this.addZeroSalesMonthsToToday(filteredData, adjustedEndDate);
    }
    
    // Update chart data and date range (use adjusted dates for proper slider width)
    this.data = dataWithGaps;
    this.options.currentStartDate = adjustedStartDate;
    this.options.currentEndDate = adjustedEndDate;
    
    // Store month boundaries for labeling (use adjusted dates)
    this.monthBoundaries = this.getMonthBoundariesForLabeling(adjustedStartDate, adjustedEndDate);
    
    // Update subtitle to show filtered period (use original period label)
    const daysWithSales = dataWithGaps.filter(d => d.sales > 0).length;
    const totalDays = dataWithGaps.length;
    
    if (this.isLabelDrillDown) {
      // Label drill-down: Show only actual data message
      if (totalDays === 0) {
        this.options.subtitle = `${periodLabel} - No data available for this period`;
      } else if (daysWithSales === totalDays) {
        this.options.subtitle = `${periodLabel} - ${totalDays} days with actual sales data`;
      } else {
        this.options.subtitle = `${periodLabel} - ${daysWithSales} days with sales, ${totalDays - daysWithSales} days with no sales (actual data only)`;
      }
    } else {
      // Manual date range: Show complete period message
      this.options.subtitle = `${periodLabel} - ${daysWithSales} days with sales out of ${totalDays} days`;
    }
    
    // Update slider visuals and re-render (use adjusted dates for proper slider width)
    this.updateSliderVisuals(adjustedStartDate, adjustedEndDate);
    
    // FAILSAFE: Ensure slider width is exactly 50px after month filtering
    if (this.isLabelDrillDown) {
      requestAnimationFrame(() => {
        this.enforceMinimumSliderWidth();
      });
    }
    
    this.render();
  }
  


  /**
   * Filter chart data to show only the selected year
   */
  filterToYear(year) {
    if (!this.options.allTimeData) {
      console.warn('No all-time data available for year filtering');
      return;
    }

    // Clear all active tabs since we're filtering to a specific year
    this.clearAllActiveTabs();

    // Create start and end dates for the selected year
    const yearStart = new Date(year, 0, 1); // January 1st
    const yearEnd = new Date(year, 11, 31, 23, 59, 59); // December 31st

    // Set period tracking for year-based filtering
    this.currentPeriodType = 'label';
    this.currentPeriodId = 'year';
    this.currentPeriodDuration = Math.ceil((yearEnd.getTime() - yearStart.getTime()) / (24 * 60 * 60 * 1000));

    // Set label-initiated flag to indicate this is a label-based filter
    this.isLabelInitiatedFilter = true;

    // Filter data to the selected year
    const yearData = this.options.allTimeData.filter(d => 
      d.dateObj >= yearStart && d.dateObj <= yearEnd
    );

    // Use today's date as the end boundary for current year, or year end for past years
    const currentDate = new Date();
    const yearEndBoundary = year === currentDate.getFullYear() ? 
      new Date(Math.min(yearEnd.getTime(), currentDate.getTime())) : yearEnd;

    // Add zero-sales months from last data point to today to show sales gaps
    const yearDataWithGaps = this.addZeroSalesMonthsToToday(yearData, yearEndBoundary);
    
    // Update chart data and date range to actual boundaries
    this.data = yearDataWithGaps;
    
    // Use year boundaries, not just data boundaries
    const actualStartDate = yearStart;
    const actualEndDate = yearEndBoundary;
    
    // Always set the current date range options so labels will be displayed
    this.options.currentStartDate = actualStartDate;
    this.options.currentEndDate = actualEndDate;

    // Store month boundaries for labeling purposes
    this.monthBoundaries = this.getMonthBoundariesForLabeling(actualStartDate, actualEndDate);

    // Reset label-initiated flag after filtering
    this.isLabelInitiatedFilter = false;

    // Update subtitle to reflect the year filter with data gap info
    const daysWithSalesInYear = yearDataWithGaps.filter(d => d.sales > 0).length;
    const gapDaysInYear = yearDataWithGaps.filter(d => d.isZeroSalesGap).length;
    
    if (daysWithSalesInYear === 0) {
      this.options.subtitle = `No sales data available for ${year}`;
    } else if (gapDaysInYear > 0) {
      this.options.subtitle = `Sales data for ${year} (${daysWithSalesInYear} days with sales, ${gapDaysInYear} day${gapDaysInYear > 1 ? 's' : ''} with no sales)`;
    } else {
      this.options.subtitle = `Sales data for ${year} (${daysWithSalesInYear} days)`;
    }

    // Set flag to indicate this is a year filter to bypass minimum range enforcement
    // This ensures the slider always shows the full year width, not minimum width
    this.isYearFilter = true;
    
    // Clear label drill-down flag for year filters (year view uses different labeling)
    this.isLabelDrillDown = false;

    // Update slider visual elements to reflect the actual data range
    this.updateSliderVisuals(actualStartDate, actualEndDate);

    // Keep the year filter flag active - DON'T reset it to false
    // The flag will be reset when user manually adjusts the slider

    // Re-render chart with filtered data
    this.render();

    // Trigger custom event for year selection
    this.containerElement.dispatchEvent(new CustomEvent('yearSelect', {
      detail: { year, dataPoints: yearData.length }
    }));
  }

  /**
   * Add individual hover events to daily sales columns (for ≤ 60 columns)
   */
  addIndividualDailySalesHoverEvents(parent, startX, columnWidth, gapBetweenColumns, chartHeight) {
    // Find all column groups and add individual hover events
    const columnGroups = parent.querySelectorAll('.snap-chart-column-group');
    
    columnGroups.forEach((columnGroup, index) => {
      const dataPoint = this.data[index];
      if (!dataPoint) return;
      
      // Calculate column position for hover area
      const columnX = startX + (index * (columnWidth + gapBetweenColumns));
      
      // Create invisible hover area for this specific column - only within grid lines
      const hoverArea = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
      hoverArea.classList.add('snap-chart-column-hover-area');
      hoverArea.setAttribute('x', columnX);
      hoverArea.setAttribute('y', 0); // Start from top of grid
      hoverArea.setAttribute('width', columnWidth);
      hoverArea.setAttribute('height', chartHeight); // Only extend to bottom of grid lines
      // Fill and pointer-events are defined in CSS
      
      // Add hover events similar to other chart types
      hoverArea.addEventListener('mouseenter', (e) => {
        // Skip hover events if slider is being dragged
        if (this.isSliderDragging) return;
        
        this.showDailySalesTooltip(e, dataPoint, index);
        this.showRoyaltiesDot(index);
      });
      
      hoverArea.addEventListener('mouseleave', () => {
        this.hideTooltip();
        this.hideAllRoyaltiesDots();
      });
      
      // Add hover area to the column group
      columnGroup.appendChild(hoverArea);
    });
  }

  /**
   * Create chart-wide hover area for smooth tooltip interaction with dense data (for > 60 columns)
   * REMOVED: This method is no longer used as hover functionality is only enabled for 30 days view
   */
  createDailySalesHoverArea(parent, startX, chartWidth, chartHeight, columnWidth, gapBetweenColumns) {
    // Method removed - hover functionality only enabled for 30 days view (month view)
    // Complex chart-wide hover area is no longer needed
  }
  
  /**
   * Handle mouse movement over daily sales chart for smooth tooltip interaction (improved accuracy)
   * REMOVED: This method is no longer used as hover functionality is only enabled for 30 days view
   */
  handleDailySalesHover(event) {
    // Method removed - hover functionality only enabled for 30 days view (month view)
    // Complex chart-wide hover handling is no longer needed
  }
  
  /**
   * Handle mouse leave for daily sales chart
   * REMOVED: This method is no longer used as hover functionality is only enabled for 30 days view
   */
  handleDailySalesMouseLeave() {
    // Method removed - hover functionality only enabled for 30 days view (month view)
    // Complex chart-wide hover handling is no longer needed
  }
  
  /**
   * Show tooltip for daily sales chart with smooth positioning
   */
  showDailySalesTooltip(event, dataPoint, index) {
    // Skip showing tooltip if slider is being dragged
    if (this.isSliderDragging) return;
    
    if (!this.tooltip || !dataPoint) return;
    
    const tooltipContent = this.generateTooltipContent(dataPoint, null);
    this.tooltip.innerHTML = tooltipContent;
    this.tooltip.classList.add('visible');
    
    // Use requestAnimationFrame for smooth positioning
    requestAnimationFrame(() => {
      // Use appropriate container as positioning reference
      // For production mode (dashboard), use containerElement which has position: relative
      // For demo mode, use chartContainer
      const positioningContainer = this.chartContainer || this.containerElement;
      const containerRect = positioningContainer.getBoundingClientRect();
      const tooltipRect = this.tooltip.getBoundingClientRect();

      // Position tooltip near mouse but avoid edges
      let tooltipX = event.clientX - containerRect.left + 15; // 15px offset from cursor
      let tooltipY = event.clientY - containerRect.top - 10; // 10px above cursor

      // Keep tooltip within container bounds
      tooltipX = Math.max(10, Math.min(tooltipX, containerRect.width - tooltipRect.width - 10));
      tooltipY = Math.max(10, Math.min(tooltipY, containerRect.height - tooltipRect.height - 10));

      this.tooltip.style.left = `${tooltipX}px`;
      this.tooltip.style.top = `${tooltipY}px`;
    });
  }
  
  /**
   * Show royalties dot for specific column index
   */
  showRoyaltiesDot(index) {
    // Hide all dots first
    this.hideAllRoyaltiesDots();

    // Show the specific dot - check both main svg and content svg for scrollable charts
    let dot = this.svg.querySelector(`.snap-chart-royalties-dot[data-column-index="${index}"]`);
    if (!dot && this.contentSvg) {
      dot = this.contentSvg.querySelector(`.snap-chart-royalties-dot[data-column-index="${index}"]`);
    }
    if (dot) {
      dot.style.opacity = '1';
    }
  }
  
  /**
   * Hide all royalties dots
   */
  hideAllRoyaltiesDots() {
    // Hide dots in main SVG
    const dots = this.svg.querySelectorAll('.snap-chart-royalties-dot');
    dots.forEach(dot => {
      dot.style.opacity = '0';
    });

    // Also hide dots in content SVG for scrollable charts
    if (this.contentSvg) {
      const contentDots = this.contentSvg.querySelectorAll('.snap-chart-royalties-dot');
      contentDots.forEach(dot => {
        dot.style.opacity = '0';
      });
    }
  }



  /**
   * Set up drag functionality for date range slider handles
   */
  setupDateRangeSliderEvents() {
    if (!this.dateRangeControls) return;
    
    const controls = this.dateRangeControls;
    let isDragging = false;
    let dragHandle = null;
    let startX = 0;
    let startY = 0; // Add startY declaration
    let hasDragged = false; // Track if drag actually occurred
    
    // Handle mouse down on handles - ONLY for scaling, prevent interference with range dragging
    const handleMouseDown = (e, handle) => {
      // Store initial mouse position but don't start dragging immediately
      // Only set up for potential drag - actual drag starts on movement
      isDragging = true;
      dragHandle = handle;
      startX = e.clientX;
      startY = e.clientY;
      hasDragged = false; // Reset drag flag on mouse down
      
      // Prevent text selection during drag
      e.preventDefault();
      // CRITICAL: Stop event propagation to prevent interference with range dragging
      e.stopPropagation();
      document.body.style.userSelect = 'none';
      
      // Don't add dragging class yet - will add when actual movement detected
    };
    
    // Handle mouse move during drag
    const handleMouseMove = (e) => {
      if (!isDragging || !dragHandle || !this.containerElement) return;
      
      // Set hasDragged to true as soon as movement occurs
      const moveThreshold = 5; // Increased threshold to detect intentional drag vs. click
      
      // Only proceed with drag if movement threshold is exceeded
      if (Math.abs(e.clientX - startX) > moveThreshold || Math.abs(e.clientY - startY) > moveThreshold) {
        // First time exceeding threshold - now we're officially dragging
        if (!hasDragged) {
          hasDragged = true;
          this.isSliderDragging = true; // Only enable dragging state when actual drag starts
          dragHandle.classList.add('dragging'); // Add visual feedback only on actual drag
        }
        
        const containerRect = this.containerElement.getBoundingClientRect();
        const canvasRect = this.containerElement.querySelector('.snap-chart-canvas').getBoundingClientRect();
        
        // Calculate mouse position relative to slider
        const mouseX = e.clientX - canvasRect.left - controls.sliderMargin;
        const clampedX = Math.max(0, Math.min(controls.sliderWidth, mouseX));
        
        // SIMPLE PROPORTIONAL POSITIONING - map position directly to data timeline
        const ratio = clampedX / controls.sliderWidth;
        
        // Calculate new date based on proportional position in data timeline
        const totalDataTimeSpan = controls.allTimeEnd.getTime() - controls.allTimeStart.getTime();
        const newDate = new Date(controls.allTimeStart.getTime() + (ratio * totalDataTimeSpan));
        
        // Data boundaries are already represented by the track - no additional clamping needed
        // The track IS the data timeline, so any position on the track is valid data
        
        // Update handle position and filter data
        if (dragHandle.classList.contains('left-handle')) {
          // When dragging left handle, allow expanding the range but prevent it from going past the right handle
          const currentEndDate = this.options.currentEndDate;
          
          // Clamp the new start date to data boundaries
          const clampedStartDate = new Date(Math.max(newDate.getTime(), controls.allTimeStart.getTime()));
          
          // Don't allow start date to be later than end date
          const finalStartDate = clampedStartDate >= currentEndDate ? 
            new Date(currentEndDate.getTime() - (24 * 60 * 60 * 1000)) : // 1 day before end date
            clampedStartDate;
          
          // PERFORMANCE: Update visuals immediately, throttle expensive data updates
          this.options.currentStartDate = finalStartDate;
          this.updateSliderVisuals(finalStartDate, currentEndDate);
          this.scheduleDataUpdate(finalStartDate, null);
        } else {
          // When dragging right handle, allow expanding the range but prevent it from going past the left handle
          const currentStartDate = this.options.currentStartDate;
          
          // Clamp the new end date to data boundaries
          const clampedEndDate = new Date(Math.min(newDate.getTime(), controls.allTimeEnd.getTime()));
          
          // Don't allow end date to be earlier than start date
          const finalEndDate = clampedEndDate <= currentStartDate ? 
            new Date(currentStartDate.getTime() + (24 * 60 * 60 * 1000)) : // 1 day after start date
            clampedEndDate;
          
          // PERFORMANCE: Update visuals immediately, throttle expensive data updates
          this.options.currentEndDate = finalEndDate;
          this.updateSliderVisuals(currentStartDate, finalEndDate);
          this.scheduleDataUpdate(null, finalEndDate);
        }
      }
    };
    
    // Handle mouse up
    const handleMouseUp = () => {
      if (isDragging && dragHandle) {
        // Only need to remove dragging class if we actually started dragging
        if (hasDragged && dragHandle !== 'range') {
          dragHandle.classList.remove('dragging');
        }
        
        // PERFORMANCE: Ensure final update is applied when drag ends
        if (hasDragged && (this.pendingStartDate || this.pendingEndDate)) {
          // Cancel any pending throttled update
          if (this.rafId) {
            cancelAnimationFrame(this.rafId);
            this.rafId = null;
          }
          // Apply final update immediately
          this.updateDateRange(this.pendingStartDate, this.pendingEndDate);
          this.pendingStartDate = null;
          this.pendingEndDate = null;
        }
        
        isDragging = false;
        this.isSliderDragging = false;
        
        dragHandle = null;
        document.body.style.userSelect = '';
      }
    };
    
    // Handle range drag (sliding the entire range left/right) - slider body dragging
    const handleRangeMouseDown = (e) => {
      // Only proceed if the event didn't come from a handle (handles should only scale)
      if (e.target.classList.contains('snap-chart-date-slider-handle')) {
        return; // Let handles handle their own scaling events
      }
      
      isDragging = true;
      dragHandle = 'range'; // Special identifier for range dragging
      startX = e.clientX;
      hasDragged = false; // Reset drag flag on mouse down
      
      // Store the current range duration to maintain it during drag
      const currentRangeDuration = this.options.currentEndDate.getTime() - this.options.currentStartDate.getTime();
      this.rangeDragInfo = {
        duration: currentRangeDuration,
        initialStartDate: new Date(this.options.currentStartDate),
        initialEndDate: new Date(this.options.currentEndDate)
      };
      
      // Prevent text selection during drag
      e.preventDefault();
      document.body.style.userSelect = 'none';
      
      // Don't change cursor or enable slider dragging yet - wait for actual movement
    };
    
    const handleRangeMouseMove = (e) => {
      if (!isDragging || dragHandle !== 'range' || !this.rangeDragInfo) return;
      
      // Set hasDragged to true as soon as movement occurs
      const moveThreshold = 5; // Increased threshold to detect intentional drag vs. click
      
      // Only proceed with drag if movement threshold is exceeded
      if (Math.abs(e.clientX - startX) > moveThreshold) {
        // First time exceeding threshold - now we're officially dragging
        if (!hasDragged) {
          hasDragged = true;
          this.isSliderDragging = true; // Only enable dragging state when actual drag starts
          controls.range.style.cursor = 'grabbing'; // Visual feedback only on actual drag
        }
        
        const deltaX = e.clientX - startX;
        const containerRect = this.containerElement.getBoundingClientRect();
        const canvasRect = this.containerElement.querySelector('.snap-chart-canvas').getBoundingClientRect();
        
        // SIMPLE PROPORTIONAL POSITIONING - map movement directly to data timeline
        const deltaRatio = deltaX / controls.sliderWidth;
        
        // Calculate time shift based on proportional movement in data timeline
        const totalDataTimeSpan = controls.allTimeEnd.getTime() - controls.allTimeStart.getTime();
        const timeShift = deltaRatio * totalDataTimeSpan;
        
        // Calculate new start and end dates
        let newStartDate = new Date(this.rangeDragInfo.initialStartDate.getTime() + timeShift);
        let newEndDate = new Date(this.rangeDragInfo.initialEndDate.getTime() + timeShift);
        
        // Clamp to data boundaries while maintaining range duration
        if (newStartDate < controls.allTimeStart) {
          // If start would go before data, clamp start and adjust end
          newStartDate = new Date(controls.allTimeStart);
          newEndDate = new Date(newStartDate.getTime() + this.rangeDragInfo.duration);
        } else if (newEndDate > controls.allTimeEnd) {
          // If end would go after data, clamp end and adjust start
          newEndDate = new Date(controls.allTimeEnd);
          newStartDate = new Date(newEndDate.getTime() - this.rangeDragInfo.duration);
        }
        
        // PERFORMANCE: Update visuals immediately, throttle expensive data updates
        this.options.currentStartDate = newStartDate;
        this.options.currentEndDate = newEndDate;
        this.updateSliderVisuals(newStartDate, newEndDate);
        this.scheduleDataUpdate(newStartDate, newEndDate);
      }
    };
    
    const handleRangeMouseUp = () => {
      if (isDragging && dragHandle === 'range') {
        // PERFORMANCE: Ensure final update is applied when range drag ends
        if (hasDragged && (this.pendingStartDate || this.pendingEndDate)) {
          // Cancel any pending throttled update
          if (this.rafId) {
            cancelAnimationFrame(this.rafId);
            this.rafId = null;
          }
          // Apply final update immediately
          this.updateDateRange(this.pendingStartDate, this.pendingEndDate);
          this.pendingStartDate = null;
          this.pendingEndDate = null;
        }
        
        isDragging = false;
        dragHandle = null;
        
        // Only reset cursor if we actually started dragging
        if (hasDragged) {
          controls.range.style.cursor = 'grab';
        }
        
        document.body.style.userSelect = '';
        this.rangeDragInfo = null;
        
        // Re-enable hover events after any slider dragging
        this.isSliderDragging = false;
      }
    };
    
    // Always prevent click events on handles
    // This is critical - we don't want any click events on handles, only drag operations
    const preventHandleClick = (e) => {
      e.preventDefault();
      e.stopPropagation();
      return false;
    };

    // Handle drag indicator drag (same as range drag but with event propagation prevention)
    const handleDragIndicatorMouseDown = (e) => {
      // Prevent event from bubbling up to the range element
      e.stopPropagation();
      // Call the same range drag handler (but bypass the handle check since this is intentional)
      isDragging = true;
      dragHandle = 'range'; // Special identifier for range dragging
      startX = e.clientX;
      hasDragged = false; // Reset drag flag on mouse down
      
      // Store the current range duration to maintain it during drag
      const currentRangeDuration = this.options.currentEndDate.getTime() - this.options.currentStartDate.getTime();
      this.rangeDragInfo = {
        duration: currentRangeDuration,
        initialStartDate: new Date(this.options.currentStartDate),
        initialEndDate: new Date(this.options.currentEndDate)
      };
      
      // Prevent text selection during drag
      e.preventDefault();
      document.body.style.userSelect = 'none';
    };

    // Add event listeners
    // Handles are ONLY for scaling - they stop propagation to prevent interference with range dragging
    controls.leftHandle.addEventListener('mousedown', (e) => {
      handleMouseDown(e, controls.leftHandle);
    });
    controls.rightHandle.addEventListener('mousedown', (e) => {
      handleMouseDown(e, controls.rightHandle);
    });
    // Range body is draggable for moving the entire range (except on handles)
    controls.range.addEventListener('mousedown', handleRangeMouseDown);
    
    // Add drag functionality to drag indicators
    controls.dragIndicator1.addEventListener('mousedown', handleDragIndicatorMouseDown);
    controls.dragIndicator2.addEventListener('mousedown', handleDragIndicatorMouseDown);
    
    // Prevent click events on handles - handles should only respond to drag gestures
    controls.leftHandle.addEventListener('click', preventHandleClick);
    controls.rightHandle.addEventListener('click', preventHandleClick);
    
    // Prevent click events on drag indicators - they should only respond to drag gestures
    controls.dragIndicator1.addEventListener('click', preventHandleClick);
    controls.dragIndicator2.addEventListener('click', preventHandleClick);
    
    // No value backgrounds to add event listeners to
    
    document.addEventListener('mousemove', (e) => {
      // Handle both individual handle dragging and range dragging
      if (dragHandle === 'range') {
        handleRangeMouseMove(e);
      } else {
        handleMouseMove(e);
      }
    });
    document.addEventListener('mouseup', () => {
      handleMouseUp();
      handleRangeMouseUp();
    });
    
    // Note: Tooltip functionality removed as requested
    
    // Store references for cleanup
    this.dateRangeSliderHandlers = {
      handleMouseMove,
      handleMouseUp
    };
  }
  


  /**
   * Add zero-sales days from last data point to today to show sales gaps
   */
  addZeroSalesMonthsToToday(filteredData, endDate) {
    if (filteredData.length === 0) {
      return filteredData; // No data, return as is
    }
    
    const today = new Date();
    const lastDataDate = filteredData[filteredData.length - 1].dateObj;
    
    // If last data is already at or after today, no gaps to fill
    if (lastDataDate >= today) {
      return filteredData;
    }
    
    // Create a copy of the data to modify
    const dataWithGaps = [...filteredData];
    
    // Start from the day after the last data point
    const currentDate = new Date(lastDataDate);
    currentDate.setDate(currentDate.getDate() + 1); // Start from next day
    
    // Determine the fill boundary - when in month drill-down mode, respect the endDate boundary
    // to avoid adding days beyond the intended month
    let fillBoundary;
    if (this.isLabelDrillDown) {
      // In month drill-down mode, ONLY fill up to the end of the selected month
      // Never extend beyond the month boundary, regardless of today's date
      fillBoundary = endDate;
    } else {
      // In normal mode, fill up to today but respect endDate
      fillBoundary = new Date(Math.min(today.getTime(), endDate.getTime()));
    }
    
    // Add zero-sales data points for each DAY up to the fill boundary
    while (currentDate <= fillBoundary) {
      const month = currentDate.toLocaleDateString('en-US', { month: 'short' }).toUpperCase();
      const day = currentDate.getDate().toString().padStart(2, '0');
      const year = currentDate.getFullYear().toString().slice(-2);
      
      // Create a zero-sales data point for this day
      const zeroSalesPoint = {
        month,
        day,
        year,
        sales: 0,
        royalties: 0,
        returns: 0,
        fullDate: currentDate.toISOString().split('T')[0],
        dateObj: new Date(currentDate),
        isZeroSalesGap: true // Flag to identify gap days
      };
      
      dataWithGaps.push(zeroSalesPoint);
      
      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return dataWithGaps;
  }

  /**
   * Get month boundaries for proper labeling without filling every day
   */
  getMonthBoundariesForLabeling(startDate, endDate) {
    const boundaries = [];
    const currentDate = new Date(startDate.getFullYear(), startDate.getMonth(), 1); // Start of start month
    
    while (currentDate <= endDate) {
      const month = currentDate.toLocaleDateString('en-US', { month: 'short' }).toUpperCase();
      const year = currentDate.getFullYear().toString().slice(-2);
      
      boundaries.push({
        month,
        year,
        dateObj: new Date(currentDate),
        isMonthBoundary: true
      });
      
      // Move to next month
      currentDate.setMonth(currentDate.getMonth() + 1);
    }
    
    return boundaries;
  }

  /**
   * Check if the current date range represents the full data range (100% slider)
   */
  isShowingFullDataRange(startDate, endDate) {
    if (!this.dateRangeControls) return false;
    
    const controls = this.dateRangeControls;
    const dayInMs = 24 * 60 * 60 * 1000;
    
    // Allow for 1 day tolerance to account for minor differences
    const startDiff = Math.abs(startDate.getTime() - controls.allTimeStart.getTime());
    const endDiff = Math.abs(endDate.getTime() - controls.allTimeEnd.getTime());
    
    return startDiff <= dayInMs && endDiff <= dayInMs;
  }

  /**
   * Schedule a throttled data update during slider drag for performance
   * 
   * This prevents the lag/freeze when dragging the slider across large datasets
   * by limiting expensive chart re-renders to 60fps maximum instead of 
   * hundreds of renders per second during mouse movement.
   */
  scheduleDataUpdate(newStartDate, newEndDate) {
    // Store pending dates
    if (newStartDate) this.pendingStartDate = newStartDate;
    if (newEndDate) this.pendingEndDate = newEndDate;
    
    // If already scheduled, skip (throttle to 60fps max)
    if (this.rafId) return;
    
    this.rafId = requestAnimationFrame(() => {
      // Apply the pending updates (expensive: data filtering, gap filling, rendering)
      this.updateDateRange(this.pendingStartDate, this.pendingEndDate);
      this.rafId = null;
    });
  }

  /**
   * Update date range and refresh chart
   */
  updateDateRange(newStartDate, newEndDate) {
    if (!this.options.allTimeData || !this.dateRangeControls) return;
    
    const controls = this.dateRangeControls;
    
    // If this is a manual slider adjustment (not a year filter click),
    // reset the year filter flag to ensure minimum width enforcement
    if (this.isYearFilter && (newStartDate || newEndDate)) {
      this.isYearFilter = false;
    }
    
    // Clear label drill-down flag when user manually adjusts the date range
    // This ensures we return to normal labeling behavior for custom date ranges
    // BUT preserve the flag when scrolling within the same period type (label-based)
    if (newStartDate || newEndDate) {
      // Only clear the label drill-down flag if we're not in a label-based context
      // Preserve labels when currentPeriodType is 'label' or when recently initiated by label click
      if (this.currentPeriodType !== 'label' && !this.wasLabelInitiated) {
        this.isLabelDrillDown = false;
      }
    }
    
    // Get current dates
    let startDate = this.options.currentStartDate;
    let endDate = this.options.currentEndDate;
    
    // Check if we should maintain the current period duration during navigation
    const shouldMaintainPeriod = this.currentPeriodType === 'tab' || this.currentPeriodType === 'label';
    
    if (shouldMaintainPeriod && this.currentPeriodDuration && (newStartDate || newEndDate)) {
      // Maintain the current period duration when navigating
      const periodDurationMs = this.currentPeriodDuration * 24 * 60 * 60 * 1000;
      
      if (newStartDate && !newEndDate) {
        // Left handle moved - adjust end date to maintain duration
        endDate = new Date(newStartDate.getTime() + periodDurationMs);
        startDate = newStartDate;
      } else if (newEndDate && !newStartDate) {
        // Right handle moved - adjust start date to maintain duration
        startDate = new Date(newEndDate.getTime() - periodDurationMs);
        endDate = newEndDate;
      } else if (newStartDate && newEndDate) {
        // Both handles moved (range drag) - use provided dates
        startDate = newStartDate;
        endDate = newEndDate;
      }
      
      // Reset period tracking when user manually adjusts beyond the original period
      // BUT preserve label-based context when scrolling after label click
      if (!this.wasLabelInitiated) {
        this.currentPeriodType = 'manual';
        this.currentPeriodId = null;
        this.currentPeriodDuration = null;
      }
    } else {
      // Normal behavior - update dates as provided
      if (newStartDate) {
        startDate = newStartDate;
      }
      if (newEndDate) {
        endDate = newEndDate;
      }
    }
    
    // Check if we're showing the full data range (100% slider)
    const isFullRange = this.isShowingFullDataRange(startDate, endDate);
    
    // Ensure minimum 7 days between start and end dates (skip for year filters and full range)
    // Skip minimum width enforcement if we're maintaining a specific period from tabs/labels
    if (!this.isYearFilter && !isFullRange && this.currentPeriodType !== 'tab' && this.currentPeriodType !== 'label') {
      const dayInMs = 24 * 60 * 60 * 1000;
      const minDays = 7; // Minimum 7 days
      const minTimeSpan = minDays * dayInMs;
      
      const currentTimeSpan = endDate.getTime() - startDate.getTime();
      
      if (currentTimeSpan < minTimeSpan) {
        // If range is too small, expand it to 7 days
        const center = (startDate.getTime() + endDate.getTime()) / 2;
        startDate = new Date(center - (minTimeSpan / 2));
        endDate = new Date(center + (minTimeSpan / 2));
        
        // If expanded range goes outside data bounds, adjust while maintaining 7 days
        if (startDate < controls.allTimeStart) {
          startDate = new Date(controls.allTimeStart);
          endDate = new Date(startDate.getTime() + minTimeSpan);
        } else if (endDate > controls.allTimeEnd) {
          endDate = new Date(controls.allTimeEnd);
          startDate = new Date(endDate.getTime() - minTimeSpan);
        }
      }
    }
    
    // Filter data to new date range
    const filteredData = this.options.allTimeData.filter(d => 
      d.dateObj >= startDate && d.dateObj <= endDate
    );
    
    // Add zero-sales months from last data point to today to show sales gaps
    const dataWithGaps = this.addZeroSalesMonthsToToday(filteredData, endDate);
    
    // Update chart data and ensure current date range is always set
    this.data = dataWithGaps;
    this.options.currentStartDate = startDate;
    this.options.currentEndDate = endDate;
    
    // Store month boundaries for labeling purposes
    this.monthBoundaries = this.getMonthBoundariesForLabeling(startDate, endDate);
    
    // Update subtitle to show data status
    const totalDaysInRange = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24)) + 1;
    const daysWithSales = dataWithGaps.filter(d => d.sales > 0).length;
    const daysWithNoSales = dataWithGaps.filter(d => d.sales === 0).length;
    const gapDays = dataWithGaps.filter(d => d.isZeroSalesGap).length;
    
    if (daysWithSales === 0) {
      this.options.subtitle = `No sales data available for selected period`;
    } else if (gapDays > 0) {
      this.options.subtitle = `Sales data (${daysWithSales} days with sales, ${gapDays} day${gapDays > 1 ? 's' : ''} with no sales)`;
    } else {
      this.options.subtitle = `Sales data (${daysWithSales} days) with interactive date range selection`;
    }
    
    // Update slider visual elements
    this.updateSliderVisuals(startDate, endDate);
    
    // Check if slider range matches any tab and activate it
    this.checkSliderTabMatch(startDate, endDate);
    
    // Reset the label-initiated flag after checking tab match
    // But only if we're not in label drill-down mode to preserve scrolling behavior
    if (!this.isLabelDrillDown) {
      this.wasLabelInitiated = false;
    }
    
    // Re-render chart with new data
    this.render();
    
    // Update insights after data change
    this.updateInsights();
  }
  
  /**
   * Update slider visual elements (handles, range, labels)
   */
  updateSliderVisuals(startDate, endDate) {
    if (!this.dateRangeControls) return;
    
    const controls = this.dateRangeControls;
    
    // Check if we're showing the full data range (100% slider)
    const isFullRange = this.isShowingFullDataRange(startDate, endDate);
    
    // Check if this is a minimum width drill-down (7 days or less)
    const isMinimumDrillDown = this.isLabelDrillDown && this.isSliderAtMinimumWidth();
    
    // For minimum width drill-downs, FORCE 50px width regardless of proportional calculation
    if (isMinimumDrillDown) {
      const minRangeWidth = 50;
      const trackStart = controls.sliderMargin;
      const trackEnd = controls.sliderMargin + controls.sliderWidth;
      
      // Calculate center position based on date ratio
      const totalDataTimeSpan = controls.allTimeEnd.getTime() - controls.allTimeStart.getTime();
      const centerRatio = ((startDate.getTime() + endDate.getTime()) / 2 - controls.allTimeStart.getTime()) / totalDataTimeSpan;
      const centerPosition = trackStart + (centerRatio * controls.sliderWidth);
      
      // Position 50px range centered on the calculated center
      let startPosition = centerPosition - (minRangeWidth / 2);
      let endPosition = centerPosition + (minRangeWidth / 2);
      
      // Ensure range stays within track bounds
      if (startPosition < trackStart) {
        startPosition = trackStart;
        endPosition = trackStart + minRangeWidth;
      } else if (endPosition > trackEnd) {
        endPosition = trackEnd;
        startPosition = trackEnd - minRangeWidth;
      }
      
      const rangeWidth = endPosition - startPosition;
      
      // Update range rectangle
      controls.range.setAttribute('x', startPosition);
      controls.range.setAttribute('width', rangeWidth);
      
      // Update handle positions
      controls.leftHandle.setAttribute('cx', startPosition + 6 + 6);
      controls.rightHandle.setAttribute('cx', endPosition - 6 - 6);
      
      // Update drag indicator positions
      const centerX = startPosition + (rangeWidth / 2);
      if (controls.dragIndicator1) {
        controls.dragIndicator1.setAttribute('x', centerX - 3);
      }
      if (controls.dragIndicator2) {
        controls.dragIndicator2.setAttribute('x', centerX + 1);
      }
      
      // Update date labels
      const leftTextX = startPosition - 16;
      const rightTextX = endPosition + 16;
      const textY = controls.sliderY + (controls.sliderHeight / 2) + 1;
      
      controls.leftValueText.setAttribute('x', leftTextX);
      controls.leftValueText.setAttribute('y', textY);
      controls.rightValueText.setAttribute('x', rightTextX);
      controls.rightValueText.setAttribute('y', textY);
      controls.leftValueText.textContent = this.formatCompactDate(startDate);
      controls.rightValueText.textContent = this.formatCompactDate(endDate);
      
      return; // Exit early for month drill-downs
    }
    
    // STANDARD PROPORTIONAL POSITIONING for all other cases
    const totalDataTimeSpan = controls.allTimeEnd.getTime() - controls.allTimeStart.getTime();
    
    // Calculate proportional positions within the data timeline
    const startRatio = (startDate.getTime() - controls.allTimeStart.getTime()) / totalDataTimeSpan;
    const endRatio = (endDate.getTime() - controls.allTimeStart.getTime()) / totalDataTimeSpan;
    
    // Map ratios directly to track positions
    const trackStart = controls.sliderMargin;
    const trackEnd = controls.sliderMargin + controls.sliderWidth;
    
    let startPosition, endPosition, rangeWidth;
    
    if (this.isYearFilter || isFullRange) {
      // For year filter or full range, always span the full width of the track
      startPosition = trackStart;
      endPosition = trackEnd;
      rangeWidth = controls.sliderWidth;
    } else {
      startPosition = trackStart + (startRatio * controls.sliderWidth);
      endPosition = trackStart + (endRatio * controls.sliderWidth);
      rangeWidth = endPosition - startPosition;
    }
    
    // ALWAYS enforce minimum range width of 50px (7 days minimum) - this is mandatory
    const minRangeWidth = 50;
    if (rangeWidth < minRangeWidth && !this.isYearFilter && !isFullRange) {
      // Visual minimum: expand range while keeping it centered
      const center = (startPosition + endPosition) / 2;
      startPosition = center - (minRangeWidth / 2);
      endPosition = center + (minRangeWidth / 2);
      
      // If expanded range goes outside track bounds, adjust
      if (startPosition < trackStart) {
        startPosition = trackStart;
        endPosition = startPosition + minRangeWidth;
      } else if (endPosition > trackEnd) {
        endPosition = trackEnd;
        startPosition = endPosition - minRangeWidth;
      }
      
      rangeWidth = endPosition - startPosition;
    }
    
    // Final safety check to prevent invalid positions
    if (startPosition >= endPosition && !this.isYearFilter && !isFullRange) {
      endPosition = Math.min(startPosition + minRangeWidth, trackEnd);
      rangeWidth = endPosition - startPosition;
    }
    
    // Update range rectangle - constrained within track
    controls.range.setAttribute('x', startPosition);
    controls.range.setAttribute('width', rangeWidth);
    
    // Update handle positions (circles positioned 6px inset from range edges as specified)
    controls.leftHandle.setAttribute('cx', startPosition + 6 + 6); // 6px inset from left edge + 6px radius = handle edge 6px from range edge
    controls.rightHandle.setAttribute('cx', endPosition - 6 - 6); // 6px inset from right edge - 6px radius = handle edge 6px from range edge
    
    // Update drag indicator positions - center of range
    const centerX = startPosition + (rangeWidth / 2);
    if (controls.dragIndicator1) {
      controls.dragIndicator1.setAttribute('x', centerX - 3); // Position first bar 3px left of center
    }
    if (controls.dragIndicator2) {
      controls.dragIndicator2.setAttribute('x', centerX + 1); // Position second bar 1px right of center
    }
    
    // Position text labels with proper spacing to avoid overlap with slider
    // Start date: positioned to the left with adequate spacing, End date: positioned to the right with adequate spacing  
    const leftTextX = startPosition - 16; // 16px to the left of range start to avoid overlap
    const rightTextX = endPosition + 16; // 16px to the right of range end to avoid overlap
    
    // Position text with 1px top margin (move down by 1px)
    const textY = controls.sliderY + (controls.sliderHeight / 2) + 1; // Center vertically with slider, then move down 1px
    
    // Update date labels positioned with adequate spacing to prevent overlap
    controls.leftValueText.setAttribute('x', leftTextX);
    controls.leftValueText.setAttribute('y', textY);
    controls.rightValueText.setAttribute('x', rightTextX);
    controls.rightValueText.setAttribute('y', textY);
    
    // Update date label content
    controls.leftValueText.textContent = this.formatCompactDate(startDate);
    controls.rightValueText.textContent = this.formatCompactDate(endDate);
  }

  /**
   * FAILSAFE: Enforce minimum 50px slider width by checking actual DOM width
   * This is a bulletproof safety check that runs after filtering
   * Uses invisible correction to avoid visual jumps
   */
  enforceMinimumSliderWidth() {
    if (!this.dateRangeControls || !this.dateRangeControls.range) return;
    
    const controls = this.dateRangeControls;
    const minRangeWidth = 50;
    
    // Get the actual rendered width from the DOM
    const currentWidth = parseFloat(controls.range.getAttribute('width')) || 0;
    
    console.log(`Slider width check: Current width = ${currentWidth}px, Required = ${minRangeWidth}px`);
    
    // If current width is less than 50px, force it to exactly 50px INVISIBLY
    if (currentWidth < minRangeWidth) {
      console.log(`ENFORCING 50px slider width (was ${currentWidth}px) - INVISIBLE CORRECTION`);
      
      // STEP 1: Temporarily hide all slider elements to make correction invisible
      const sliderElements = [
        controls.range,
        controls.leftHandle,
        controls.rightHandle,
        controls.dragIndicator1,
        controls.dragIndicator2,
        controls.leftValueText,
        controls.rightValueText
      ].filter(Boolean); // Remove any null elements
      
      // Hide elements instantly
      sliderElements.forEach(element => {
        element.style.opacity = '0';
        element.style.transition = 'none'; // Disable transitions during correction
      });
      
      const trackStart = controls.sliderMargin;
      const trackEnd = controls.sliderMargin + controls.sliderWidth;
      
      // Get current position and recalculate with 50px width
      const currentX = parseFloat(controls.range.getAttribute('x')) || trackStart;
      const currentCenterX = currentX + (currentWidth / 2);
      
      // Position 50px range centered on current position
      let newStartPosition = currentCenterX - (minRangeWidth / 2);
      let newEndPosition = currentCenterX + (minRangeWidth / 2);
      
      // Ensure range stays within track bounds
      if (newStartPosition < trackStart) {
        newStartPosition = trackStart;
        newEndPosition = trackStart + minRangeWidth;
      } else if (newEndPosition > trackEnd) {
        newEndPosition = trackEnd;
        newStartPosition = trackEnd - minRangeWidth;
      }
      
      // STEP 2: Apply corrections while hidden
      controls.range.setAttribute('x', newStartPosition);
      controls.range.setAttribute('width', minRangeWidth);
      
      // Update handle positions
      controls.leftHandle.setAttribute('cx', newStartPosition + 6 + 6);
      controls.rightHandle.setAttribute('cx', newEndPosition - 6 - 6);
      
      // Update drag indicator positions
      const centerX = newStartPosition + (minRangeWidth / 2);
      if (controls.dragIndicator1) {
        controls.dragIndicator1.setAttribute('x', centerX - 3);
      }
      if (controls.dragIndicator2) {
        controls.dragIndicator2.setAttribute('x', centerX + 1);
      }
      
      // Update date label positions
      const leftTextX = newStartPosition - 16;
      const rightTextX = newEndPosition + 16;
      const textY = controls.sliderY + (controls.sliderHeight / 2) + 1;
      
      controls.leftValueText.setAttribute('x', leftTextX);
      controls.leftValueText.setAttribute('y', textY);
      controls.rightValueText.setAttribute('x', rightTextX);
      controls.rightValueText.setAttribute('y', textY);
      
      // STEP 3: Restore visibility with smooth transition after a tiny delay
      requestAnimationFrame(() => {
        sliderElements.forEach(element => {
          element.style.transition = 'opacity 0.15s ease-out'; // Smooth fade-in
          element.style.opacity = '1';
        });
        
        // Clean up transition styles after animation completes
        setTimeout(() => {
          sliderElements.forEach(element => {
            element.style.transition = '';
          });
        }, 200);
      });
      
      console.log(`Slider width ENFORCED: New width = ${minRangeWidth}px at position ${newStartPosition}px (INVISIBLE)`);
    } else {
      console.log(`Slider width OK: ${currentWidth}px >= ${minRangeWidth}px`);
    }
  }

  /**
   * Add column interaction events
   */
  addColumnEvents(columnGroup, dataPoint, index) {
    columnGroup.addEventListener('mouseenter', (e) => {
      // Skip hover events if slider is being dragged
      if (this.isSliderDragging) return;
      
      // Clear any existing hover debounce timer
      if (this.hoverDebounceTimer) {
        clearTimeout(this.hoverDebounceTimer);
      }
      
      // Debounce tooltip display for performance during fast mouse movement
      this.hoverDebounceTimer = setTimeout(() => {
        // Get comparison data if available and process it to ensure marketplace structure is correct
        let comparisonDataPoint = null;
        if (this.options.compareMode && this.options.compareData && this.options.compareData.length > index) {
          comparisonDataPoint = this.processMarketplaceData(this.options.compareData[index]);
        }
        
        this.showTooltip(e, dataPoint, index, comparisonDataPoint);
        // Show corresponding dot - check both main svg and content svg for scrollable charts
        let dot = this.svg.querySelector(`.snap-chart-royalties-dot[data-column-index="${index}"]`);
        if (!dot && this.contentSvg) {
          dot = this.contentSvg.querySelector(`.snap-chart-royalties-dot[data-column-index="${index}"]`);
        }
        if (dot) {
          dot.style.opacity = '1';
        }
      }, 50); // 50ms debounce for responsive feel while preventing excessive updates
    });
    
    columnGroup.addEventListener('mouseleave', () => {
      // Clear hover debounce timer on mouse leave
      if (this.hoverDebounceTimer) {
        clearTimeout(this.hoverDebounceTimer);
        this.hoverDebounceTimer = null;
      }
      
      this.hideTooltip();
      // Hide corresponding dot - check both main svg and content svg for scrollable charts
      let dot = this.svg.querySelector(`.snap-chart-royalties-dot[data-column-index="${index}"]`);
      if (!dot && this.contentSvg) {
        dot = this.contentSvg.querySelector(`.snap-chart-royalties-dot[data-column-index="${index}"]`);
      }
      if (dot) {
        dot.style.opacity = '0';
      }
    });
    
    // Click functionality removed - only hover tooltips now
  }

  /**
   * Add unified hover area spanning main column, gap, and comparison column
   * This provides a single unified hover experience for comparison mode
   */
  addUnifiedComparisonHoverArea(parent, startX, totalWidth, chartHeight, mainDataPoint, comparisonDataPoint, index) {
    // Create invisible hover area spanning the entire comparison group
    const unifiedHoverArea = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    unifiedHoverArea.classList.add('snap-chart-unified-comparison-hover-area');
    unifiedHoverArea.setAttribute('x', startX);
    unifiedHoverArea.setAttribute('y', 0);
    unifiedHoverArea.setAttribute('width', totalWidth);
    unifiedHoverArea.setAttribute('height', chartHeight);
    unifiedHoverArea.setAttribute('fill', 'transparent');
    unifiedHoverArea.setAttribute('pointer-events', 'all');

    // Add hover events that show comparison tooltip
    unifiedHoverArea.addEventListener('mouseenter', (e) => {
      // Skip hover events if slider is being dragged
      if (this.isSliderDragging) return;

      // Clear any existing hover debounce timer
      if (this.hoverDebounceTimer) {
        clearTimeout(this.hoverDebounceTimer);
      }

      // Use debounced hover to prevent flickering
      this.hoverDebounceTimer = setTimeout(() => {
        this.showTooltip(e, mainDataPoint, index, comparisonDataPoint);
        this.showRoyaltiesDot(index);
      }, 50);
    });

    unifiedHoverArea.addEventListener('mouseleave', () => {
      // Clear hover debounce timer on mouse leave
      if (this.hoverDebounceTimer) {
        clearTimeout(this.hoverDebounceTimer);
        this.hoverDebounceTimer = null;
      }

      this.hideTooltip();
      this.hideAllRoyaltiesDots();
    });

    parent.appendChild(unifiedHoverArea);
  }

  /**
   * Add gap hover area between main and comparison columns
   * This ensures tooltips appear when hovering over the gap space
   */
  addGapHoverArea(parent, gapStartX, gapWidth, chartHeight, dataPoint, index) {
    // Create invisible hover area for the gap
    const gapHoverArea = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
    gapHoverArea.classList.add('snap-chart-gap-hover-area');
    gapHoverArea.setAttribute('x', gapStartX);
    gapHoverArea.setAttribute('y', 0);
    gapHoverArea.setAttribute('width', gapWidth);
    gapHoverArea.setAttribute('height', chartHeight);
    gapHoverArea.setAttribute('fill', 'transparent');
    gapHoverArea.setAttribute('pointer-events', 'all');

    // Add hover events that show the same tooltip as the main column
    gapHoverArea.addEventListener('mouseenter', (e) => {
      // Skip hover events if slider is being dragged
      if (this.isSliderDragging) return;

      // Clear any existing hover debounce timer
      if (this.hoverDebounceTimer) {
        clearTimeout(this.hoverDebounceTimer);
      }

      // Use debounced hover to prevent excessive tooltip updates
      this.hoverDebounceTimer = setTimeout(() => {
        // Get comparison data if available - this ensures gap hover shows both current and comparison data
        const comparisonDataPoint = (this.options.compareData && index < this.options.compareData.length)
          ? this.options.compareData[index]
          : null;

        // Show tooltip with both current and comparison data (same as hovering over columns)
        this.showTooltip(e, dataPoint, comparisonDataPoint);

        // Show royalties dots for both main and comparison data
        const dots = this.svg.querySelectorAll('.snap-chart-royalties-dot');
        dots.forEach((dot, dotIndex) => {
          if (dotIndex === index || (this.options.compareMode && dotIndex === index + this.data.length)) {
            dot.style.opacity = '1';
          }
        });
      }, 50); // 50ms debounce for responsive feel while preventing excessive updates
    });

    gapHoverArea.addEventListener('mouseleave', () => {
      // Clear hover debounce timer on mouse leave
      if (this.hoverDebounceTimer) {
        clearTimeout(this.hoverDebounceTimer);
        this.hoverDebounceTimer = null;
      }

      this.hideTooltip();
      this.hideAllRoyaltiesDots();
    });

    parent.appendChild(gapHoverArea);
  }

  /**
   * Add comparison column interaction events
   * When hovering over comparison columns, show tooltip with both current and comparison data
   */
  addComparisonColumnEvents(columnGroup, comparisonDataPoint, index) {
    columnGroup.addEventListener('mouseenter', (e) => {
      // Skip hover events if slider is being dragged
      if (this.isSliderDragging) return;
      
      // Clear any existing hover debounce timer
      if (this.hoverDebounceTimer) {
        clearTimeout(this.hoverDebounceTimer);
      }
      
      // Debounce tooltip display for performance during fast mouse movement
      this.hoverDebounceTimer = setTimeout(() => {
        // Get the current data point for this index
        const currentDataPoint = this.data[index];
        if (!currentDataPoint) return;
        
        // Process comparison data to ensure marketplace structure is correct
        const processedComparisonData = this.processMarketplaceData(comparisonDataPoint);
        
        // Show tooltip with current data as main and comparison data as comparison
        this.showTooltip(e, currentDataPoint, index, processedComparisonData);
        
        // Show corresponding dot - check both main svg and content svg for scrollable charts
        let dot = this.svg.querySelector(`.snap-chart-royalties-dot[data-column-index="${index}"]`);
        if (!dot && this.contentSvg) {
          dot = this.contentSvg.querySelector(`.snap-chart-royalties-dot[data-column-index="${index}"]`);
        }
        if (dot) {
          dot.style.opacity = '1';
        }
      }, 50); // 50ms debounce for responsive feel while preventing excessive updates
    });
    
    columnGroup.addEventListener('mouseleave', () => {
      // Clear hover debounce timer on mouse leave
      if (this.hoverDebounceTimer) {
        clearTimeout(this.hoverDebounceTimer);
        this.hoverDebounceTimer = null;
      }
      
      this.hideTooltip();
      // Hide corresponding dot - check both main svg and content svg for scrollable charts
      let dot = this.svg.querySelector(`.snap-chart-royalties-dot[data-column-index="${index}"]`);
      if (!dot && this.contentSvg) {
        dot = this.contentSvg.querySelector(`.snap-chart-royalties-dot[data-column-index="${index}"]`);
      }
      if (dot) {
        dot.style.opacity = '0';
      }
    });
  }

  /**
   * Add simple comparison column interaction events (for non-stacked charts)
   * When hovering over simple comparison columns, show tooltip with both current and comparison data
   */
  addSimpleComparisonColumnEvents(columnElement, comparisonDataPoint, index) {
    columnElement.addEventListener('mouseenter', (e) => {
      // Skip hover events if slider is being dragged
      if (this.isSliderDragging) return;
      
      // Clear any existing hover debounce timer
      if (this.hoverDebounceTimer) {
        clearTimeout(this.hoverDebounceTimer);
      }
      
      // Debounce tooltip display for performance during fast mouse movement
      this.hoverDebounceTimer = setTimeout(() => {
        // Get the current data point for this index
        const currentDataPoint = this.data[index];
        if (!currentDataPoint) return;
        
        // Process comparison data to ensure marketplace structure is correct
        const processedComparisonData = this.processMarketplaceData(comparisonDataPoint);
        
        // Show tooltip with current data as main and comparison data as comparison
        this.showTooltip(e, currentDataPoint, index, processedComparisonData);
        
        // Show corresponding dot - check both main svg and content svg for scrollable charts
        let dot = this.svg.querySelector(`.snap-chart-royalties-dot[data-column-index="${index}"]`);
        if (!dot && this.contentSvg) {
          dot = this.contentSvg.querySelector(`.snap-chart-royalties-dot[data-column-index="${index}"]`);
        }
        if (dot) {
          dot.style.opacity = '1';
        }
      }, 50); // 50ms debounce for responsive feel while preventing excessive updates
    });
    
    columnElement.addEventListener('mouseleave', () => {
      // Clear hover debounce timer on mouse leave
      if (this.hoverDebounceTimer) {
        clearTimeout(this.hoverDebounceTimer);
        this.hoverDebounceTimer = null;
      }
      
      this.hideTooltip();
      // Hide corresponding dot - check both main svg and content svg for scrollable charts
      let dot = this.svg.querySelector(`.snap-chart-royalties-dot[data-column-index="${index}"]`);
      if (!dot && this.contentSvg) {
        dot = this.contentSvg.querySelector(`.snap-chart-royalties-dot[data-column-index="${index}"]`);
      }
      if (dot) {
        dot.style.opacity = '0';
      }
    });
  }

  /**
   * Determine if a marketplace should be shown in tooltip based on focus mode
   */
  shouldShowMarketplaceInTooltip(value, index, dataPoint) {
    // Check if we have marketplace focus information from the dashboard
    const isMarketplaceFocusActive = window.globalMarketplaceFocus && window.globalMarketplaceFocus !== 'all';

    if (isMarketplaceFocusActive) {
      // In single marketplace focus mode, show the selected marketplace even if value is 0
      const countryCode = (dataPoint.labels && dataPoint.labels[index]) || `Country ${index + 1}`;
      const isFocusedMarketplace = countryCode.toLowerCase() === window.globalMarketplaceFocus.toLowerCase();

      if (isFocusedMarketplace) {
        return true; // Always show focused marketplace, even with 0 sales
      } else {
        return value > 0; // Show non-focused marketplaces only if they have sales
      }
    } else {
      // In "all marketplaces" mode, only show marketplaces with sales > 0
      return value > 0;
    }
  }

  /**
   * Show tooltip with smart positioning
   */
  showTooltip(event, dataPoint, index, comparisonDataPoint = null) {
    // Skip showing tooltip if slider is being dragged
    if (this.isSliderDragging) return;

    if (!this.tooltip) return;
    const tooltipContent = this.generateTooltipContent(dataPoint, comparisonDataPoint);
    this.tooltip.innerHTML = tooltipContent;
    this.tooltip.classList.add('visible');

    // Add enhanced class for stacked column charts
    if (this.type === 'stacked-column' || this.type === 'scrollable-stacked-column') {
      this.tooltip.classList.add('enhanced');
      
      // Add comparison-mode class for comparison mode
      const hasComparison = comparisonDataPoint && this.options.compareMode;
      if (hasComparison) {
        this.tooltip.classList.add('comparison-mode');
      } else {
        this.tooltip.classList.remove('comparison-mode');
      }
      
      // Calculate and set dynamic width for enhanced tooltips
      this.setDynamicTooltipWidth(dataPoint, comparisonDataPoint);
    } else {
      this.tooltip.classList.remove('enhanced', 'comparison-mode');
    }

    // Load flag icons for enhanced tooltips
    if (this.type === 'stacked-column' || this.type === 'scrollable-stacked-column') {
      this.loadTooltipFlags(this.tooltip);
    }

    // Use requestAnimationFrame to ensure positioning happens after layout is complete
    requestAnimationFrame(() => {
      // Find the dot element directly - check both main svg and content svg for scrollable charts
      let dot = this.svg.querySelector(`.snap-chart-royalties-dot[data-column-index="${index}"]`);
      if (!dot && this.contentSvg) {
        dot = this.contentSvg.querySelector(`.snap-chart-royalties-dot[data-column-index="${index}"]`);
      }
      if (!dot) {
        console.warn('SnapChart: Could not find dot for index', index);
        return;
      }

      // Get the dot's bounding box in screen coordinates
      const dotRect = dot.getBoundingClientRect();
      // Use appropriate container as positioning reference
      // For production mode (dashboard), use containerElement which has position: relative
      // For demo mode, use chartContainer
      const positioningContainer = this.chartContainer || this.containerElement;
      const containerRect = positioningContainer.getBoundingClientRect();
      const tooltipRect = this.tooltip.getBoundingClientRect();



      // Calculate dot center relative to container
      const dotCenterX = dotRect.left + dotRect.width / 2 - containerRect.left;
      const dotCenterY = dotRect.top + dotRect.height / 2 - containerRect.top;
      
      // Determine which half of the chart this column is in
      // For scrollable charts, consider the visible area, not total data length
      let isFirstHalf;
      
      if (this.type === 'scrollable-stacked-column' && this.scrollableContainer) {
        // For scrollable charts, determine position based on visible area
        const positioningContainer = this.chartContainer || this.canvas;
        const chartContainerRect = positioningContainer.getBoundingClientRect();
        const visibleCenterX = chartContainerRect.width / 2;
        const columnPositionInContainer = dotCenterX;
        
        // If column is in the left half of the visible area, show tooltip on right
        // If column is in the right half of the visible area, show tooltip on left
        isFirstHalf = columnPositionInContainer < visibleCenterX;
      } else {
        // For standard charts, use the original logic based on column index
        const totalColumns = this.data.length;
        isFirstHalf = index < Math.floor(totalColumns / 2);
      }
      
      let tooltipX, tooltipY;
      
      if (isFirstHalf) {
        // First half: show tooltip on the RIGHT side of the column
        tooltipX = dotCenterX + 20; // 20px to the right of dot center
      } else {
        // Second half: show tooltip on the LEFT side of the column
        tooltipX = dotCenterX - tooltipRect.width - 20; // 20px to the left of dot center
      }
      
      // Center tooltip vertically on the dot
      tooltipY = dotCenterY - tooltipRect.height / 2;
      
      // Ensure tooltip doesn't go outside container bounds
      tooltipX = Math.max(10, Math.min(tooltipX, containerRect.width - tooltipRect.width - 10));
      tooltipY = Math.max(10, Math.min(tooltipY, containerRect.height - tooltipRect.height - 10));



      this.tooltip.style.left = `${tooltipX}px`;
      this.tooltip.style.top = `${tooltipY}px`;


    });
  }
  
  /**
   * Hide tooltip
   */
  hideTooltip() {
    if (this.tooltip) {
      this.tooltip.classList.remove('visible');
      // Reset width to allow CSS min-width to take effect for next tooltip
      this.tooltip.style.width = '';
    }
  }
  
  /**
   * Calculate and set dynamic width image.pngced tooltips
   * Ensures minimum 24px gap between labels and values
   */
  setDynamicTooltipWidth(dataPoint, comparisonDataPoint = null) {
    if (!this.tooltip || !dataPoint) return;
    
    // Create a temporary canvas to measure text width
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Set font to match tooltip text - use fallback fonts and increase weight
    ctx.font = '600 12px Amazon Ember, -apple-system, BlinkMacSystemFont, "Segoe UI", Arial, sans-serif';
    
    const hasComparison = comparisonDataPoint && this.options.compareMode;
    const minGap = 24; // Minimum gap between label and value
    const padding = 16; // Tooltip padding
    const minWidth = hasComparison ? 380 : 190; // Base minimum width
    
    let maxRequiredWidth = minWidth;
    
    // Calculate width for single column or main column in comparison mode
    const mainColumnWidth = this.calculateTooltipColumnWidth(ctx, dataPoint, minGap, padding, comparisonDataPoint);
    
    if (hasComparison) {
      // For comparison mode, calculate both columns (comparison column doesn't get percentage changes)
      const comparisonColumnWidth = this.calculateTooltipColumnWidth(ctx, comparisonDataPoint, minGap, padding);
      const dividerWidth = 1; // Vertical divider width
      const columnPadding = 24; // 12px padding on each side of divider
      
      // Total width = main column + divider + comparison column + column padding
      maxRequiredWidth = Math.max(minWidth, mainColumnWidth + dividerWidth + comparisonColumnWidth + columnPadding);
      
      // Debug logging for comparison mode
      console.log('Dynamic Tooltip Width (Comparison Mode):', {
        mainColumnWidth,
        comparisonColumnWidth,
        dividerWidth,
        columnPadding,
        minWidth,
        calculatedWidth: maxRequiredWidth,
        dataPoint: { sales: dataPoint.sales, royalties: dataPoint.royalties },
        comparisonDataPoint: { sales: comparisonDataPoint.sales, royalties: comparisonDataPoint.royalties }
      });
    } else {
      // Single column mode
      maxRequiredWidth = Math.max(minWidth, mainColumnWidth);
      
      // Debug logging for single mode
      console.log('Dynamic Tooltip Width (Single Mode):', {
        mainColumnWidth,
        minWidth,
        calculatedWidth: maxRequiredWidth,
        dataPoint: { sales: dataPoint.sales, royalties: dataPoint.royalties }
      });
    }
    
    // Apply the calculated width
    this.tooltip.style.width = `${maxRequiredWidth}px`;
    
    // Clean up
    canvas.remove();
  }
  
  /**
   * Calculate required width for a single tooltip column
   */
  calculateTooltipColumnWidth(ctx, dataPoint, minGap, padding, comparisonDataPoint = null) {
    if (!dataPoint) return 0;
    
    const sales = typeof dataPoint.sales === 'number' ? dataPoint.sales : 0;
    const royalties = typeof dataPoint.royalties === 'number' ? dataPoint.royalties : 0;
    const returns = typeof dataPoint.returns === 'number' ? dataPoint.returns : (typeof dataPoint.change === 'number' ? dataPoint.change : 0);
    
    // Calculate percentage changes if comparison data is available
    let salesPercentageChange = null;
    let royaltiesPercentageChange = null;
    let returnsPercentageChange = null;
    
    if (comparisonDataPoint) {
      const compSales = typeof comparisonDataPoint.sales === 'number' ? comparisonDataPoint.sales : 0;
      const compRoyalties = typeof comparisonDataPoint.royalties === 'number' ? comparisonDataPoint.royalties : 0;
      const compReturns = typeof comparisonDataPoint.returns === 'number' ? comparisonDataPoint.returns : (typeof comparisonDataPoint.change === 'number' ? comparisonDataPoint.change : 0);
      
      // Handle edge cases for division by zero

      // Sales percentage change
      if (compSales === 0 && sales === 0) {
        // Both zero: no comparison to show
        salesPercentageChange = null;
      } else if (compSales === 0 && sales > 0) {
        // Previous zero, current has value: new data (treat as 100% increase)
        salesPercentageChange = 100;
      } else if (compSales > 0) {
        // Normal calculation
        salesPercentageChange = ((sales - compSales) / compSales) * 100;
      }

      // Royalties percentage change
      if (compRoyalties === 0 && royalties === 0) {
        // Both zero: no comparison to show
        royaltiesPercentageChange = null;
      } else if (compRoyalties === 0 && royalties > 0) {
        // Previous zero, current has value: new data (treat as 100% increase)
        royaltiesPercentageChange = 100;
      } else if (compRoyalties > 0) {
        // Normal calculation
        royaltiesPercentageChange = ((royalties - compRoyalties) / compRoyalties) * 100;
      }

      // Returns percentage change
      if (compReturns === 0 && returns === 0) {
        // Both zero: no comparison to show
        returnsPercentageChange = null;
      } else if (compReturns === 0 && returns > 0) {
        // Previous zero, current has returns: new returns (treat as 100% increase - bad)
        returnsPercentageChange = 100;
      } else if (compReturns > 0) {
        // Normal calculation
        returnsPercentageChange = ((returns - compReturns) / compReturns) * 100;
      }
    }
    
    let maxRequiredWidth = 0;
    
    // Measure main items (Sales, Royalties, Returns) including percentage changes
    const mainItems = [
      { 
        label: 'Total Sales', 
        value: sales.toLocaleString(),
        percentageChange: salesPercentageChange
      },
      { 
        label: 'Royalties', 
        value: `$${royalties.toLocaleString()}`,
        percentageChange: royaltiesPercentageChange
      },
      { 
        label: 'Returns', 
        value: returns > 0 ? `(-${Math.abs(returns)})` : `(0)`,
        percentageChange: returnsPercentageChange
      }
    ];
    
    // Only show returns for non-daily sales charts
    const itemsToMeasure = this.type === 'daily-sales-history' ? mainItems.slice(0, 2) : mainItems;
    
    itemsToMeasure.forEach(item => {
      const labelWidth = ctx.measureText(item.label).width;
      const valueWidth = ctx.measureText(item.value).width;
      
      // Measure percentage change text if it exists
      let percentageChangeWidth = 0;
      if (item.percentageChange !== null && Math.abs(item.percentageChange) > 0.01) {
        const isPositive = item.percentageChange >= 0;
        const changeText = `${isPositive ? '+' : ''}${item.percentageChange.toFixed(1)}%`;
        percentageChangeWidth = ctx.measureText(changeText).width + 20; // +20 for icon and spacing
      }
      
      // Total width = label + percentage change + gap + value + padding
      const requiredWidth = labelWidth + percentageChangeWidth + minGap + valueWidth + (padding * 2);
      maxRequiredWidth = Math.max(maxRequiredWidth, requiredWidth);
      
      // Debug logging for main items
      console.log(`Main item "${item.label}": labelWidth=${labelWidth.toFixed(1)}, percentageChangeWidth=${percentageChangeWidth.toFixed(1)}, valueWidth=${valueWidth.toFixed(1)}, requiredWidth=${requiredWidth.toFixed(1)}`);
    });
    
    // Measure marketplace breakdown items if present
    if (this.type !== 'daily-sales-history' && dataPoint.values && Array.isArray(dataPoint.values)) {
      dataPoint.values.forEach((value, index) => {
        if (typeof value === 'number' && value > 0) {
          const countryCode = (dataPoint.labels && dataPoint.labels[index]) || `Country ${index + 1}`;
          const mapping = this.marketplaceMapping[countryCode];
          const countryLabel = mapping ? mapping.label : countryCode;
          const currency = mapping ? mapping.currency : '$';
          
          // Calculate individual returns
          let individualReturns = 0;
          if (dataPoint.marketplaces && dataPoint.marketplaces[index]) {
            individualReturns = dataPoint.marketplaces[index].returns || 0;
          } else {
            const shouldHaveZeroReturns = dataPoint.zeroReturnsMarketplaceIndex !== undefined && 
                                         dataPoint.zeroReturnsMarketplaceIndex === index;
            individualReturns = shouldHaveZeroReturns ? 0 : (returns > 0 ? Math.round((value / sales) * returns) : 0);
          }
          
          // Individual royalties
          let individualRoyalties = 0;
          if (dataPoint.marketplaces && dataPoint.marketplaces[index]) {
            individualRoyalties = dataPoint.marketplaces[index].royalties || 0;
          } else {
            individualRoyalties = royalties > 0 ? Math.round((value / sales) * royalties * 100) / 100 : 0;
          }
          
          // Measure marketplace item
          const marketplaceLabel = countryLabel; // Flag icon width not included in text measurement
          const returnsDisplay = individualReturns > 0 ? `(-${individualReturns})` : '(0)';
          const marketplaceValue = `${value.toLocaleString()} ${returnsDisplay}`;
          const marketplaceRoyalties = `${currency}${individualRoyalties.toLocaleString()}`;
          
          const labelWidth = ctx.measureText(marketplaceLabel).width + 40; // Increased from 32 to 40 for flag icon and color dot
          const valueWidth = Math.max(
            ctx.measureText(marketplaceValue).width,
            ctx.measureText(marketplaceRoyalties).width
          );
          const requiredWidth = labelWidth + valueWidth + minGap + (padding * 2);
          maxRequiredWidth = Math.max(maxRequiredWidth, requiredWidth);
          
          // Debug logging for marketplace items
          console.log(`Marketplace "${countryLabel}": labelWidth=${labelWidth.toFixed(1)}, valueWidth=${valueWidth.toFixed(1)}, requiredWidth=${requiredWidth.toFixed(1)}`);
        }
      });
    }
    
    console.log(`Column max required width: ${maxRequiredWidth.toFixed(1)}`);
    return maxRequiredWidth;
  }
  
  /**
   * Generate tooltip content for stacked columns with compare support
   */
  generateTooltipContent(dataPoint, comparisonDataPoint = null) {
    if (!dataPoint) {
      console.warn('SnapChart: No data point provided for tooltip');
      return 'No data available';
    }
    
    try {
      // Check if this is a stacked column chart that supports enhanced tooltips
      const isStackedColumn = this.type === 'stacked-column' || this.type === 'scrollable-stacked-column';
      const hasComparison = comparisonDataPoint && this.options.compareMode;
      
      // For non-stacked column charts, use legacy tooltip format
      if (!isStackedColumn) {
        return this.generateLegacyTooltipContent(dataPoint);
      }
      
      // Enhanced tooltip for stacked columns
      const month = dataPoint.month || 'Unknown';
      const day = dataPoint.day || '';
      const year = dataPoint.year || '';
      
      let dateDisplay = `${month} ${day}`;
      if (year) {
        dateDisplay += `, '${year}`;
      }
      
      // Build enhanced tooltip structure
      let content = `<div class="snap-chart-tooltip-enhanced">`;
      
      if (hasComparison) {
        // Two-column layout for comparison with vertical divider
        // NO single date header - each column has its own date
        content += `<div class="snap-chart-tooltip-content">`;
        content += `<div class="snap-chart-tooltip-columns">`;
        
        // Main data column with date on left and "Current" on right
        content += `<div class="snap-chart-tooltip-column main">`;
        content += `<div class="snap-chart-tooltip-column-header-row">`;
        content += `<div class="snap-chart-tooltip-column-date">${dateDisplay}</div>`;
        content += `<div class="snap-chart-tooltip-column-header">Current</div>`;
        content += `</div>`;
        content += this.generateTooltipColumnContent(dataPoint, false, comparisonDataPoint);
        content += `</div>`;
        
        // Vertical divider
        content += `<div class="snap-chart-tooltip-divider"></div>`;
        
        // Comparison data column with date on left and "Previous" on right
        const compMonth = comparisonDataPoint.month || 'Unknown';
        const compDay = comparisonDataPoint.day || '';
        const compYear = comparisonDataPoint.year || '';
        let compDateDisplay = `${compMonth} ${compDay}`;
        if (compYear) {
          compDateDisplay += `, '${compYear}`;
        }
        
        content += `<div class="snap-chart-tooltip-column comparison">`;
        content += `<div class="snap-chart-tooltip-column-header-row">`;
        content += `<div class="snap-chart-tooltip-column-date">${compDateDisplay}</div>`;
        content += `<div class="snap-chart-tooltip-column-header">Previous</div>`;
        content += `</div>`;
        content += this.generateTooltipColumnContent(comparisonDataPoint, true);
        content += `</div>`;
        
        content += `</div>`; // Close columns
      } else {
        // Single column layout with header date
        content += `<div class="snap-chart-tooltip-header">${dateDisplay}</div>`;
        content += `<div class="snap-chart-tooltip-content">`;
        content += this.generateTooltipColumnContent(dataPoint, false, null);
      }
      
      content += `</div>`; // Close content
      content += `</div>`; // Close enhanced tooltip
      
      return content;
    } catch (error) {
      console.error('SnapChart: Error generating tooltip content:', error);
      return 'Error displaying data';
    }
  }
  
  /**
   * Generate tooltip column content with marketplace flags and percentage changes
   */
  generateTooltipColumnContent(dataPoint, isComparison = false, comparisonDataPoint = null) {
    if (!dataPoint) {
      return '<div class="snap-chart-tooltip-no-data">No data available</div>';
    }
    
    const sales = typeof dataPoint.sales === 'number' ? dataPoint.sales : 0;
    const royalties = typeof dataPoint.royalties === 'number' ? dataPoint.royalties : 0;
    const returns = typeof dataPoint.returns === 'number' ? dataPoint.returns : (typeof dataPoint.change === 'number' ? dataPoint.change : 0);
    
    // Calculate percentage changes for current data (only if we have comparison data and this is not the comparison column)
    let salesPercentageChange = null;
    let royaltiesPercentageChange = null;
    let returnsPercentageChange = null;
    
    if (comparisonDataPoint && !isComparison) {
      const compSales = typeof comparisonDataPoint.sales === 'number' ? comparisonDataPoint.sales : 0;
      const compRoyalties = typeof comparisonDataPoint.royalties === 'number' ? comparisonDataPoint.royalties : 0;
      const compReturns = typeof comparisonDataPoint.returns === 'number' ? comparisonDataPoint.returns : (typeof comparisonDataPoint.change === 'number' ? comparisonDataPoint.change : 0);
      
      // Calculate percentage change: ((current - previous) / previous) * 100
      // Handle edge cases for division by zero

      // Sales percentage change
      if (compSales === 0 && sales === 0) {
        // Both zero: no comparison to show
        salesPercentageChange = null;
      } else if (compSales === 0 && sales > 0) {
        // Previous zero, current has value: new data (treat as 100% increase)
        salesPercentageChange = 100;
      } else if (compSales > 0) {
        // Normal calculation
        salesPercentageChange = ((sales - compSales) / compSales) * 100;
      }

      // Royalties percentage change
      if (compRoyalties === 0 && royalties === 0) {
        // Both zero: no comparison to show
        royaltiesPercentageChange = null;
      } else if (compRoyalties === 0 && royalties > 0) {
        // Previous zero, current has value: new data (treat as 100% increase)
        royaltiesPercentageChange = 100;
      } else if (compRoyalties > 0) {
        // Normal calculation
        royaltiesPercentageChange = ((royalties - compRoyalties) / compRoyalties) * 100;
      }

      // Returns percentage change
      if (compReturns === 0 && returns === 0) {
        // Both zero: no comparison to show
        returnsPercentageChange = null;
      } else if (compReturns === 0 && returns > 0) {
        // Previous zero, current has returns: new returns (treat as 100% increase - bad)
        returnsPercentageChange = 100;
      } else if (compReturns > 0) {
        // Normal calculation
        returnsPercentageChange = ((returns - compReturns) / compReturns) * 100;
      }
    }
    
    let content = '';
    
    // Total sales, royalties, and returns
    content += `<div class="snap-chart-tooltip-totals">`;
    content += `<div class="snap-chart-tooltip-total-item">`;
    content += `<span class="snap-chart-tooltip-label">Total Sales</span>`;
    content += `<div class="snap-chart-tooltip-value-with-change">`;
    
    // Add percentage change indicator for sales (only for current data in comparison mode)
    if (salesPercentageChange !== null && Math.abs(salesPercentageChange) > 0.01) {
      const isPositive = salesPercentageChange >= 0;
      const iconName = isPositive ? 'up-per-ic.svg' : 'down-per-ic.svg';
      const changeColor = isPositive ? '#04AE2C' : '#FF391F';
      const changeText = `${isPositive ? '+' : ''}${salesPercentageChange.toFixed(1)}%`;

      content += `<span class="snap-chart-tooltip-percentage-change ${isPositive ? 'positive' : 'negative'}" style="color: ${changeColor}">`;
      content += `${changeText}`;
      content += `<img src="assets/${iconName}" alt="${isPositive ? 'up' : 'down'}" class="snap-chart-tooltip-change-icon">`;
      content += `</span>`;
    }
    
    content += `<span class="snap-chart-tooltip-value">${sales.toLocaleString()}</span>`;
    content += `</div>`;
    content += `</div>`;
    content += `<div class="snap-chart-tooltip-total-item">`;
    content += `<span class="snap-chart-tooltip-label">Royalties</span>`;
    content += `<div class="snap-chart-tooltip-value-with-change">`;
    
    // Add percentage change indicator for royalties (only for current data in comparison mode)
    if (royaltiesPercentageChange !== null && Math.abs(royaltiesPercentageChange) > 0.01) {
      const isPositive = royaltiesPercentageChange >= 0;
      const iconName = isPositive ? 'up-per-ic.svg' : 'down-per-ic.svg';
      const changeColor = isPositive ? '#04AE2C' : '#FF391F';
      const changeText = `${isPositive ? '+' : ''}${royaltiesPercentageChange.toFixed(1)}%`;

      content += `<span class="snap-chart-tooltip-percentage-change ${isPositive ? 'positive' : 'negative'}" style="color: ${changeColor}">`;
      content += `${changeText}`;
      content += `<img src="assets/${iconName}" alt="${isPositive ? 'up' : 'down'}" class="snap-chart-tooltip-change-icon">`;
      content += `</span>`;
    }
    
    content += `<span class="snap-chart-tooltip-value">$${royalties.toLocaleString()}</span>`;
    content += `</div>`;
    content += `</div>`;
    
    // Show returns for all chart types including daily sales history
    content += `<div class="snap-chart-tooltip-total-item">`;
    content += `<span class="snap-chart-tooltip-label">Returns</span>`;
    content += `<div class="snap-chart-tooltip-value-with-change">`;
    
    // Add percentage change indicator for returns (only for current data in comparison mode)
    // Returns have inverted color logic: up = red (bad), down = green (good)
    if (returnsPercentageChange !== null && Math.abs(returnsPercentageChange) > 0.01) {
      const isPositive = returnsPercentageChange >= 0;
      const iconName = isPositive ? 'return-up-per-ic.svg' : 'return-down-per-ic.svg';
      const changeColor = isPositive ? '#FF391F' : '#04AE2C'; // Inverted: up = red, down = green
      const changeText = `${isPositive ? '+' : ''}${returnsPercentageChange.toFixed(1)}%`;

      content += `<span class="snap-chart-tooltip-percentage-change ${isPositive ? 'negative' : 'positive'}" style="color: ${changeColor}">`;
      content += `${changeText}`;
      content += `<img src="assets/${iconName}" alt="${isPositive ? 'up' : 'down'}" class="snap-chart-tooltip-change-icon">`;
      content += `</span>`;
    }
    
    // Apply appropriate styling: red for non-zero returns, same as sales color with 50% opacity for zero returns
    const returnsClass = returns > 0 ? 'returns' : 'zero-returns';
    let returnsDisplay = returns > 0 ? `(-${Math.abs(returns)})` : `(0)`;
    
    // Add return ratio for non-zero returns
    if (returns > 0 && sales > 0) {
      const returnRatio = (returns / sales) * 100;
      returnsDisplay += ` <span style="color: #FF391F">${returnRatio.toFixed(2)}%</span>`;
    }
    
    content += `<span class="snap-chart-tooltip-value ${returnsClass}">${returnsDisplay}</span>`;
    content += `</div>`;
    content += `</div>`;
    
    content += `</div>`;

    // Marketplace breakdown with flags and colored dots - only for regular stacked column charts
    // Hide marketplace breakdown when single marketplace is selected (marketplace focus mode)
    const isMarketplaceFocusActive = window.globalMarketplaceFocus && window.globalMarketplaceFocus !== 'all';
    const shouldShowMarketplaceBreakdown = !isMarketplaceFocusActive;

    if (this.type !== 'daily-sales-history' &&
        dataPoint.values && Array.isArray(dataPoint.values) && dataPoint.values.length > 0 &&
        shouldShowMarketplaceBreakdown) {
      content += `<div class="snap-chart-tooltip-breakdown">`;
      content += `<div class="snap-chart-tooltip-breakdown-items">`;

      dataPoint.values.forEach((value, index) => {
        // Determine if we should show this marketplace based on focus mode
        const shouldShowMarketplace = this.shouldShowMarketplaceInTooltip(value, index, dataPoint);

        if (typeof value === 'number' && shouldShowMarketplace) {
          const countryCode = (dataPoint.labels && dataPoint.labels[index]) || `Country ${index + 1}`;
          const mapping = this.marketplaceMapping[countryCode];
          const countryLabel = mapping ? mapping.label : countryCode;
          const currency = mapping ? mapping.currency : '$';
          const color = mapping ? mapping.color : '#8562FF';
          
          // Get actual marketplace data if available, otherwise calculate proportionally
          let individualRoyalties = 0;
          let individualReturns = 0;
          
          if (dataPoint.marketplaces && dataPoint.marketplaces[index]) {
            // Use actual marketplace data
            const marketplace = dataPoint.marketplaces[index];
            individualRoyalties = marketplace.royalties || 0;
            individualReturns = marketplace.returns || 0;
          } else {
            // Fallback to proportional calculation for legacy data
            individualRoyalties = royalties > 0 ? Math.round((value / sales) * royalties * 100) / 100 : 0;
            
            // Check if this marketplace should have 0 returns
            const shouldHaveZeroReturns = dataPoint.zeroReturnsMarketplaceIndex !== undefined && 
                                         dataPoint.zeroReturnsMarketplaceIndex === index;
            individualReturns = shouldHaveZeroReturns ? 0 : (returns > 0 ? Math.round((value / sales) * returns) : 0);
          }
          
          content += `<div class="snap-chart-tooltip-breakdown-item">`;
          content += `<div class="snap-chart-tooltip-marketplace">`;
          content += this.getFlagIconHtml(countryCode);
          content += `<span class="snap-chart-tooltip-marketplace-label">${countryLabel}</span>`;
          // Only show color dot for main columns (not comparison columns)
          if (!isComparison) {
            // Apply 60% opacity to background and 100% opacity to border to match chart segments
            const colorWithOpacity = color + '99'; // Add 60% opacity (99 in hex)
            content += `<div class="snap-chart-tooltip-color-dot" style="background-color: ${colorWithOpacity}; border-color: ${color};"></div>`;
          }
          content += `</div>`;
          content += `<div class="snap-chart-tooltip-marketplace-values">`;
          
          // Show returns with appropriate styling
          let returnsDisplay = '';
          if (individualReturns > 0) {
            returnsDisplay = `<span class="returns">(-${individualReturns})</span>`;
          } else {
            returnsDisplay = `<span class="zero-returns">(0)</span>`;
          }
          
          content += `<span class="snap-chart-tooltip-marketplace-value">${value.toLocaleString()} ${returnsDisplay}</span>`;
          content += `<span class="snap-chart-tooltip-marketplace-royalties">${currency}${individualRoyalties.toLocaleString()}</span>`;
          content += `</div>`;
          content += `</div>`;
        }
      });
      
      content += `</div>`; // Close breakdown items
      content += `</div>`; // Close breakdown
    }
    
    return content;
  }
  
  /**
   * Generate legacy tooltip content for non-stacked column charts
   */
  generateLegacyTooltipContent(dataPoint) {
      const month = dataPoint.month || 'Unknown';
      const day = dataPoint.day || '';
      const year = dataPoint.year || '';
      
      // Enhanced date display for daily sales history
      let dateDisplay;
      if (this.type === 'daily-sales-history') {
        dateDisplay = `${month} ${day}${year ? `, 20${year}` : ''}`;
      } else {
        dateDisplay = `${month} ${day}`;
      }
      
      let content = '';
      
      const sales = typeof dataPoint.sales === 'number' ? dataPoint.sales : 0;
      const royalties = typeof dataPoint.royalties === 'number' ? dataPoint.royalties : 0;
      const returns = typeof dataPoint.returns === 'number' ? dataPoint.returns : 0;
      
      // Calculate dynamic width for Daily Sales History to ensure 16px minimum gap
      let containerWidth = 120; // Default minimum width
      if (this.type === 'daily-sales-history') {
        containerWidth = this.calculateDailySalesTooltipWidth(dataPoint, sales, royalties, returns, dateDisplay);
      }
      
      // Add container with dynamic width for Daily Sales History
      if (this.type === 'daily-sales-history') {
        content += `<div style="width: ${containerWidth}px;">`;
        content += `<strong>${dateDisplay}</strong>`;
        content += `<hr style="border: none; border-top: 1px solid rgba(51, 51, 51, 0.5); margin: 8px 0;">`;
      } else {
        content += `<strong>${dateDisplay}</strong><br>`;
      }
      
      // Check if day has any activity (sales, royalties, or returns)
      const hasAnyActivity = sales > 0 || royalties > 0 || returns > 0;
      
      if (sales === 0 && !hasAnyActivity) {
        // For Daily Sales History, use lighter gray and don't show royalties line
        if (this.type === 'daily-sales-history') {
          content += `<span style="color: #9CA3AF; opacity: 0.9;">No sales this day</span><br>`;
        } else {
          // For other chart types, use updated lighter gray color
          content += `<span style="color: #9CA3AF; opacity: 0.8;">No sales this day</span><br>`;
          content += `Royalties: $0<br>`;
        }
      } else {
        if (this.type === 'daily-sales-history') {
          // For Daily Sales, use structured format with left-right alignment
          content += `<div style="display: flex; justify-content: space-between; margin-bottom: 4px;">`;
          content += `<span>Total Sales</span><span>${sales.toLocaleString()}</span>`;
          content += `</div>`;
          content += `<div style="display: flex; justify-content: space-between; margin-bottom: 4px;">`;
          content += `<span>Royalties</span><span>$${royalties.toLocaleString()}</span>`;
          content += `</div>`;
          
          // Add returns display for daily sales history
          content += `<div style="display: flex; justify-content: space-between;">`;
          content += `<span>Returns</span>`;
          
          if (returns > 0) {
            let returnsDisplay = `(-${returns})`;
            if (sales > 0) {
              const returnRatio = (returns / sales) * 100;
              returnsDisplay += ` <span style="color: #FF391F">${returnRatio.toFixed(2)}%</span>`;
            }
            content += `<span style="color: #FF391F">${returnsDisplay}</span>`;
          } else {
            content += `<span style="color: white; opacity: 0.5">(0)</span>`;
          }
          content += `</div>`;
        } else {
          content += `Total Sales: ${sales.toLocaleString()} units<br>`;
          content += `Royalties: $${royalties.toLocaleString()}<br>`;
        }
      }
      
      if (dataPoint.values && Array.isArray(dataPoint.values) && dataPoint.values.length > 0) {
        content += `<br><strong>Sales by Country:</strong><br>`;
        dataPoint.values.forEach((value, index) => {
          if (typeof value === 'number' && value > 0) {
            const countryLabel = (dataPoint.labels && dataPoint.labels[index]) || `Country ${index + 1}`;
            content += `${countryLabel}: ${value.toLocaleString()} units<br>`;
          }
        });
      }
      
      // Close the container div for Daily Sales History
      if (this.type === 'daily-sales-history') {
        content += `</div>`;
      }
      
      return content;
  }

  /**
   * Calculate dynamic width for daily sales tooltip to ensure 16px minimum gap
   */
  calculateDailySalesTooltipWidth(dataPoint, sales, royalties, returns, dateDisplay) {
    // Create a temporary canvas for text measurement
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Use the same font as the tooltip
    ctx.font = '500 12px Amazon Ember, -apple-system, BlinkMacSystemFont, "Segoe UI", Arial, sans-serif';
    
    const minGap = 16; // Minimum 16px gap between text and values
    const padding = 16; // 8px padding on each side (total 16px)
    let maxRequiredWidth = 120; // Minimum width
    
    // Measure date display width
    const dateWidth = ctx.measureText(dateDisplay).width + padding;
    maxRequiredWidth = Math.max(maxRequiredWidth, dateWidth);
    
    // Check if day has any activity
    const hasAnyActivity = sales > 0 || royalties > 0 || returns > 0;
    
    if (sales === 0 && !hasAnyActivity) {
      // Measure "No sales this day" text
      const noSalesWidth = ctx.measureText('No sales this day').width + padding;
      maxRequiredWidth = Math.max(maxRequiredWidth, noSalesWidth);
    } else {
      // Measure each row: label + gap + value
      const items = [
        { label: 'Total Sales', value: sales.toLocaleString() },
        { label: 'Royalties', value: `$${royalties.toLocaleString()}` }
      ];
      
      // Add returns if present
      if (returns > 0) {
        let returnsValue = `(-${returns})`;
        if (sales > 0) {
          const returnRatio = (returns / sales) * 100;
          returnsValue += ` ${returnRatio.toFixed(2)}%`;
        }
        items.push({ label: 'Returns', value: returnsValue });
      } else {
        items.push({ label: 'Returns', value: '(0)' });
      }
      
      // Calculate required width for each item
      items.forEach(item => {
        const labelWidth = ctx.measureText(item.label).width;
        const valueWidth = ctx.measureText(item.value).width;
        const requiredWidth = labelWidth + minGap + valueWidth + padding;
        maxRequiredWidth = Math.max(maxRequiredWidth, requiredWidth);
      });
    }
    
    // Add some buffer and round up to nearest 10px for cleaner appearance
    return Math.ceil((maxRequiredWidth + 10) / 10) * 10;
  }
  
  /**
   * Select a column
   */
  selectColumn(index) {
    // Remove existing selection
    const existingSelection = this.svg.querySelectorAll('.snap-chart-selection-indicator, .snap-chart-selection-dot');
    existingSelection.forEach(el => el.remove());
    
    // Add new selection
    const chartGroup = this.svg.querySelector('g');
    if (chartGroup) {
      const columnWidth = this.getCanvasWidth() / this.data.length;
      const chartHeight = this.options.height - 60; // Adjust for padding
      this.drawSelectionIndicator(chartGroup, index, columnWidth, chartHeight);
    }
    
    // Trigger custom event
    this.containerElement.dispatchEvent(new CustomEvent('columnSelect', {
      detail: { index, data: this.data[index] }
    }));
  }
  
  /**
   * Set up event listeners
   */
  setupEventListeners() {
    if (this.options.responsive) {
      // Use ResizeObserver for better performance and immediate updates
      if (window.ResizeObserver) {
        this.resizeObserver = new ResizeObserver((entries) => {
          for (const entry of entries) {
            this.handleResizeObserver(entry);
          }
        });
        this.resizeObserver.observe(this.containerElement);
      } else {
        // Fallback to window resize for older browsers
        window.addEventListener('resize', this.handleResize);
      }
    }

    // Theme change listener - store reference for cleanup
    this.themeObserver = new MutationObserver(this.handleThemeChange);
    this.themeObserver.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    });
  }
  
  /**
   * Handle ResizeObserver changes (modern browsers)
   */
  handleResizeObserver(entry) {
    try {
      if (!this.isInitialized) return;

      const newWidth = entry.contentRect.width;

      // Immediate SVG viewBox update for instant visual feedback
      if (this.svg) {
        this.svg.setAttribute('viewBox', `0 0 ${newWidth} ${this.options.height}`);
      }

      // Immediate re-render without debounce for smooth responsiveness
      requestAnimationFrame(() => {
        if (this.isInitialized && this.containerElement) {
          this.updateSizeImmediate();
        }
      });
    } catch (error) {
      console.error('SnapChart: Error handling resize observer:', error);
    }
  }

  /**
   * Handle window resize (fallback for older browsers)
   */
  handleResize() {
    try {
      if (!this.isInitialized) return;

      // Immediate visual feedback for smooth responsiveness
      if (this.svg && this.containerElement) {
        const newWidth = this.getCanvasWidth();
        this.svg.setAttribute('viewBox', `0 0 ${newWidth} ${this.options.height}`);
      }

      // Minimal debounce for fallback mode
      clearTimeout(this.resizeTimeout);
      this.resizeTimeout = setTimeout(() => {
        if (this.isInitialized && this.containerElement) {
          this.updateSize();
        }
      }, 50);
    } catch (error) {
      console.error('SnapChart: Error handling resize:', error);
    }
  }
  
  /**
   * Handle theme change
   */
  handleThemeChange() {
    // Theme change is handled automatically by CSS variables
    // This method can be extended for theme-specific logic
  }
  
  /**
   * Update chart size immediately (for ResizeObserver)
   */
  updateSizeImmediate() {
    try {
      if (!this.svg || !this.containerElement) return;

      const newWidth = this.getCanvasWidth();
      this.svg.setAttribute('viewBox', `0 0 ${newWidth} ${this.options.height}`);

      // Immediate re-render for smooth responsiveness
      this.render();

      // Update custom scrollbar after resize
      if (this.scrollableContainer && this.updateCustomScrollbar) {
        this.updateCustomScrollbar(this.scrollableContainer);
      }
    } catch (error) {
      console.error('SnapChart: Error updating size immediately:', error);
    }
  }

  /**
   * Update chart size (fallback method)
   */
  updateSize() {
    try {
      if (!this.svg || !this.containerElement) return;

      const newWidth = this.getCanvasWidth();
      this.svg.setAttribute('viewBox', `0 0 ${newWidth} ${this.options.height}`);
      this.render();

      // Update custom scrollbar after resize
      if (this.scrollableContainer && this.updateCustomScrollbar) {
        requestAnimationFrame(() => {
          this.updateCustomScrollbar(this.scrollableContainer);
        });
      }
    } catch (error) {
      console.error('SnapChart: Error updating size:', error);
    }
  }
  
  /**
   * Update chart data
   */
  updateData(newData) {
    try {
      if (!newData || !Array.isArray(newData)) {
        console.warn('SnapChart: Invalid data provided to updateData');
        return;
      }
      
      // Process the data to ensure totals are calculated correctly
      const processedData = this.processEditorData(newData);
      
      // Update both data sources to ensure consistency
      this.data = processedData;
      this.options.allTimeData = processedData; // Sync allTimeData with current data for tab filtering
      
      this.render();
      
      // Update tab states after data change to reflect new data availability
      setTimeout(() => {
        this.updateTabStates();
        // Update insights after data change
        this.updateInsights();
      }, 0);
    } catch (error) {
      console.error('SnapChart: Error updating data:', error);
    }
  }
  
  /**
   * Load and cache SVG flag icon
   */
  async loadFlagSvg(countryCode) {
    try {
      // Check cache first
      if (this.flagSvgCache[countryCode]) {
        return this.flagSvgCache[countryCode];
      }
      
      const mapping = this.marketplaceMapping[countryCode];
      if (!mapping) {
        console.warn(`SnapChart: No flag mapping found for country code: ${countryCode}`);
        return null;
      }
      
      // Use the main app's assets directory for flag icons
      const flagPath = `assets/${mapping.flag}`;
      const svgContent = `<img src="${flagPath}" alt="${countryCode}" class="snap-chart-flag-img" />`;
      
      // Cache the SVG image HTML
      this.flagSvgCache[countryCode] = svgContent;
      console.log(`SnapChart: Using main app assets for ${countryCode} flag`);
      return svgContent;
    } catch (error) {
      console.error(`SnapChart: Error loading flag SVG for ${countryCode}:`, error);
      return null;
    }
  }
  
  /**
   * Get flag icon HTML for a country code
   */
  getFlagIconHtml(countryCode) {
    const mapping = this.marketplaceMapping[countryCode];
    if (!mapping) {
      return '';
    }
    
    // Return a placeholder that will be replaced with actual SVG
    return `<span class="snap-chart-flag-icon" data-country="${countryCode}">${countryCode}</span>`;
  }
  
  /**
   * Load and insert flag SVGs into tooltip after it's rendered
   */
  async loadTooltipFlags(tooltipElement) {
    try {
      const flagPlaceholders = tooltipElement.querySelectorAll('.snap-chart-flag-icon[data-country]');
      
      for (const placeholder of flagPlaceholders) {
        const countryCode = placeholder.getAttribute('data-country');
        const svgContent = await this.loadFlagSvg(countryCode);
        
        if (svgContent) {
          placeholder.innerHTML = svgContent;
          placeholder.classList.add('loaded');
        }
      }
    } catch (error) {
      console.error('SnapChart: Error loading tooltip flags:', error);
    }
  }
  
  /**
   * Destroy chart and clean up
   */
  destroy() {
    try {
      // Clean up resize observers and event listeners
      if (this.options.responsive) {
        if (this.resizeObserver) {
          this.resizeObserver.disconnect();
          this.resizeObserver = null;
        } else {
          window.removeEventListener('resize', this.handleResize);
        }
      }

      // Clean up resize timeout
      if (this.resizeTimeout) {
        clearTimeout(this.resizeTimeout);
        this.resizeTimeout = null;
      }

      // Clean up theme observer
      if (this.themeObserver) {
        this.themeObserver.disconnect();
        this.themeObserver = null;
      }
      
      // Clean up date range slider event listeners
      if (this.dateRangeSliderHandlers) {
        document.removeEventListener('mousemove', this.dateRangeSliderHandlers.handleMouseMove);
        document.removeEventListener('mouseup', this.dateRangeSliderHandlers.handleMouseUp);
        this.dateRangeSliderHandlers = null;
      }
      
      // Clean up performance optimization timers
      if (this.rafId) {
        cancelAnimationFrame(this.rafId);
        this.rafId = null;
      }
      this.pendingStartDate = null;
      this.pendingEndDate = null;
      

      
      // Clean up daily sales hover info (legacy cleanup - no longer used)
      if (this.dailySalesHoverInfo && this.dailySalesHoverInfo.debounceTimer) {
        clearTimeout(this.dailySalesHoverInfo.debounceTimer);
        this.dailySalesHoverInfo = null;
      }
      
      // Clean up hover debounce timer
      if (this.hoverDebounceTimer) {
        clearTimeout(this.hoverDebounceTimer);
        this.hoverDebounceTimer = null;
      }
      
      // Clean up scrollbar (consolidated cleanup method)
      this.cleanupScrollbar();

      // Clean up Today vs Previous Years specific resources
      if (this.todayVsPreviousYearsResizeObserver) {
        this.todayVsPreviousYearsResizeObserver.disconnect();
        this.todayVsPreviousYearsResizeObserver = null;
      }

      if (this.todayVsPreviousYearsWindowResizeHandler) {
        window.removeEventListener('resize', this.todayVsPreviousYearsWindowResizeHandler);
        this.todayVsPreviousYearsWindowResizeHandler = null;
      }

      if (this.resizeTimeout) {
        clearTimeout(this.resizeTimeout);
        this.resizeTimeout = null;
      }

      // Clean up DOM and remove chart content
      if (this.containerElement) {
        this.containerElement.innerHTML = '';
        this.containerElement.classList.remove('snap-chart');
        this.containerElement.removeAttribute('tabindex');
        this.containerElement.removeAttribute('role');
        this.containerElement.removeAttribute('aria-label');
      }
      
      // Reset references
      this.svg = null;
      this.tooltip = null;
      this.containerElement = null;
      this.isInitialized = false;
      this.isLabelDrillDown = false;
    } catch (error) {
      console.error('SnapChart: Error during cleanup:', error);
    }
    }
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = SnapChart;
} else if (typeof window !== 'undefined') {
  window.SnapChart = SnapChart;
} 